import { create } from "zustand";
import Pinch<PERSON>oom from "@/lib/pinch-zoom/pinch-zoom";

interface PinchZoomStore {
  pinchZoomInstance: PinchZoom | undefined;
  setPinchZoomInstance: (zoomInstance: PinchZoom) => void;
  disableDraggable: () => void;
  enableDraggable: () => void;
}

export const usePinchZoomStore = create<PinchZoomStore>((set, get) => ({
  pinchZoomInstance: undefined,
  setPinchZoomInstance: (zoomInstance) =>
    set({ pinchZoomInstance: zoomInstance }),
  disableDraggable: () => {
    const { pinchZoomInstance } = get();
    if (pinchZoomInstance) {
      pinchZoomInstance.setDraggableGlobal(false);
    }
  },
  enableDraggable: () => {
    const { pinchZoomInstance } = get();
    if (pinchZoomInstance) {
      pinchZoomInstance.setDraggableGlobal(true);
    }
  },
}));

import React, {
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
} from "react";
import { useDebounceFn, useEventListener } from "ahooks";
import { IPenLine } from "@/db/app-db";
import { savePenLines, useGetPagePenLines } from "@/db/pen-data-hooks";
import {
  ILayerScale,
  ILayerSize,
  usePdfInfoStore,
} from "@/store/pdf-info-store";
import { useRectToolboxStore } from "@/store/rect-toolbox-store";
import { usePinchZoomStore } from "@/store/pinch-zoom-store";
import { PenType, usePenStates } from "@/store/pen-info-store";

type DrawEvent = TouchEvent | MouseEvent;

interface DrawPenProps {
  pageIndex: number;
  pageSize: ILayerSize;
  pageScale: ILayerScale;
}

const drawNormalPenPolyline = (
  ctx: CanvasRenderingContext2D,
  line: IPenLine
) => {
  if (!line.points || line.points.length < 2) return;
  const points = line.points;
  ctx.beginPath();
  ctx.moveTo(points[0]!, points[1]!);
  for (let i = 2; i < points.length; i += 2) {
    ctx.lineTo(points[i]!, points[i + 1]!);
  }
  ctx.strokeStyle = line.color;
  ctx.lineWidth = line.width / 2;
  ctx.lineCap = "round";
  ctx.lineJoin = "round";
  ctx.stroke();
};

const drawStraightLine = (
  ctx: CanvasRenderingContext2D,
  line: IPenLine,
  isHighlight: boolean = false
) => {
  if (!line.points || line.points.length < 4) return;
  const points = line.points;
  ctx.beginPath();
  ctx.moveTo(points[0]!, points[1]!);
  ctx.lineTo(points[2]!, points[3]!);
  ctx.strokeStyle = line.color;
  ctx.lineWidth = isHighlight ? line.width : line.width / 2;
  ctx.lineCap = isHighlight ? "butt" : "round";
  ctx.lineJoin = "round";
  ctx.stroke();
};

const drawHighlightPolyline = (
  ctx: CanvasRenderingContext2D,
  line: IPenLine
) => {
  if (!line.points || line.points.length < 2) return;
  const points = line.points;
  ctx.beginPath();
  ctx.moveTo(points[0]!, points[1]!);
  for (let i = 2; i < points.length; i += 2) {
    ctx.lineTo(points[i]!, points[i + 1]!);
  }
  ctx.strokeStyle = line.color;
  ctx.lineWidth = line.width;
  ctx.lineCap = "butt";
  ctx.lineJoin = "round";
  ctx.stroke();
};

const drawDashLine = (ctx: CanvasRenderingContext2D, line: IPenLine) => {
  if (!line.points || line.points.length < 4) return;
  const points = line.points;
  ctx.beginPath();
  ctx.moveTo(points[0]!, points[1]!);
  ctx.lineTo(points[2]!, points[3]!);
  ctx.strokeStyle = line.color;
  ctx.lineWidth = line.width / 2;
  ctx.lineCap = "round";
  ctx.lineJoin = "round";
  // Dynamic dash pattern based on line width
  const dashLength = Math.max(4, line.width * 1.5);
  const gapLength = Math.max(2, line.width * 0.75);
  ctx.setLineDash([dashLength, gapLength]);
  ctx.stroke();
  ctx.setLineDash([]); // Reset
};

const drawEraserLine = (ctx: CanvasRenderingContext2D, line: IPenLine) => {
  if (!line.points || line.points.length < 2) return;
  const points = line.points;
  ctx.save();
  ctx.globalCompositeOperation = "destination-out";
  ctx.beginPath();
  ctx.moveTo(points[0]!, points[1]!);
  for (let i = 2; i < points.length; i += 2) {
    ctx.lineTo(points[i]!, points[i + 1]!);
  }
  ctx.strokeStyle = "rgba(0,0,0,1)";
  ctx.lineWidth = line.width * 5;
  ctx.lineCap = "round";
  ctx.lineJoin = "round";
  ctx.stroke();
  ctx.restore();
};

export const DrawPen = (props: DrawPenProps) => {
  const { pageIndex, pageSize, pageScale } = props;
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentFileId] = usePdfInfoStore((state) => [state.currentFileId]);
  const [setActivePageNumber] = usePdfInfoStore((state) => [
    state.setActivePageNumber,
  ]);
  const [zoomScale] = useRectToolboxStore((state) => [state.zoomScale]);
  const [enableDraggable, disableDraggable] = usePinchZoomStore((state) => [
    state.enableDraggable,
    state.disableDraggable,
  ]);
  const [penType, penColor, penWidth] = usePenStates((state) => [
    state.penType,
    state.penColor,
    state.penWidth,
  ]);
  const penLines = useGetPagePenLines(currentFileId, pageIndex);

  // State for the line being drawn (to trigger re-render for visual feedback)
  const [drawingLineForUIRender, setDrawingLineForUIRender] = useState<
    IPenLine | undefined
  >();
  // Ref to hold the current line data for event handlers, avoiding useCallback dependency issues
  const currentDrawingLineRef = useRef<IPenLine | null>(null);
  const isDrawing = useRef(false);

  const getCanvasPosition = useCallback(
    (event: DrawEvent): { x: number; y: number } | null => {
      const canvas = canvasRef.current;
      if (!canvas) return null;

      const rect = canvas.getBoundingClientRect();
      let clientX, clientY;

      if (event instanceof MouseEvent) {
        clientX = event.clientX;
        clientY = event.clientY;
      } else if (event instanceof TouchEvent) {
        if (event.touches.length === 0) return null;
        clientX = event.touches[0]!.clientX;
        clientY = event.touches[0]!.clientY;
      } else {
        return null;
      }

      // Get position relative to canvas
      const x = (clientX - rect.left) / pageScale.x / zoomScale;
      const y = (clientY - rect.top) / pageScale.y / zoomScale;

      return { x, y };
    },
    [pageScale.x, pageScale.y, zoomScale]
  );

  const handleMouseDown = useCallback(
    (e: DrawEvent) => {
      isDrawing.current = true;
      if (e.cancelable) e.preventDefault();
      const pointer = getCanvasPosition(e);
      if (!pointer) return;

      const newLine: IPenLine = {
        fileId: currentFileId,
        uid: Date.now(),
        pageIndex: pageIndex,
        type: penType,
        color: penColor,
        width: penWidth,
        points: [pointer.x, pointer.y],
      };
      currentDrawingLineRef.current = newLine;
      setDrawingLineForUIRender(newLine);
      setActivePageNumber(pageIndex);
      disableDraggable();
    },
    [
      currentFileId,
      pageIndex,
      penType,
      penColor,
      penWidth,
      getCanvasPosition,
      setActivePageNumber,
      disableDraggable,
    ]
  );

  const handleMouseMove = useCallback(
    (e: DrawEvent) => {
      if (!isDrawing.current) return;
      if (e.cancelable) e.preventDefault();
      const pointer = getCanvasPosition(e);

      const currentLine = currentDrawingLineRef.current;
      if (!currentLine || !pointer) {
        return;
      }

      let newPoints;
      if (
        penType === PenType.LINE ||
        penType === PenType.DOTTED_LINE ||
        penType === PenType.HIGHLIGHT_PEN_STRAIGHT
      ) {
        newPoints = [
          currentLine.points[0] ?? 0,
          currentLine.points[1] ?? 0,
          pointer.x,
          pointer.y,
        ];
      } else {
        newPoints = currentLine.points.concat([pointer.x, pointer.y]);
      }
      currentLine.points = newPoints;
      setDrawingLineForUIRender({ ...currentLine });
    },
    [penType, getCanvasPosition]
  );

  const handleMouseUp = useCallback(() => {
    if (!isDrawing.current) return;
    isDrawing.current = false;
    const finalLine = currentDrawingLineRef.current;

    if (
      !finalLine ||
      finalLine.points.length <
        (penType === PenType.LINE ||
        penType === PenType.DOTTED_LINE ||
        penType === PenType.HIGHLIGHT_PEN_STRAIGHT
          ? 4 // Straight lines need two points (4 coordinates)
          : 2) // Polylines need at least one point (2 coordinates)
    ) {
      currentDrawingLineRef.current = null;
      setDrawingLineForUIRender(undefined);
      return;
    }

    const lineToSave = { ...finalLine, finished: true };
    savePenLines(lineToSave);
    // setNewPenLine({ ...newPenLine, uid: Date.now() }); // keep last line for performance tip
    currentDrawingLineRef.current = null;
    enableDraggable();
  }, [penType, enableDraggable]);

  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      if (e.touches.length === 1) {
        handleMouseDown(e);
      }
    },
    [handleMouseDown]
  );

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (e.touches.length === 1) {
        handleMouseMove(e);
      }
    },
    [handleMouseMove]
  );

  useEventListener("mousedown", handleMouseDown, {
    target: canvasRef,
    passive: false,
  });
  useEventListener("mousemove", handleMouseMove, {
    target: canvasRef,
    passive: false,
  });
  useEventListener("touchstart", handleTouchStart, {
    target: canvasRef,
    passive: false,
  });
  useEventListener("touchmove", handleTouchMove, {
    target: canvasRef,
    passive: false,
  });
  useEventListener("mouseup", handleMouseUp, { target: window });
  useEventListener("touchend", handleMouseUp, { target: window });

  const penLinesToDraw = useMemo(() => {
    if (drawingLineForUIRender) {
      const isLineInPenLines = penLines.some(
        (line) => line.uid === drawingLineForUIRender.uid
      );

      if (isLineInPenLines) {
        return penLines;
      } else {
        return [...penLines, drawingLineForUIRender];
      }
    }
    return penLines;
  }, [penLines, drawingLineForUIRender]);

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) {
      return;
    }

    // Setting canvas width/height clears the canvas and resets the context (like transforms)
    canvas.width = pageSize.width;
    canvas.height = pageSize.height;
    canvas.style.width = `${pageSize.width}px`;
    canvas.style.height = `${pageSize.height}px`;
    // Apply scale after dimensions are set
    ctx.scale(pageScale.x, pageScale.y);

    penLinesToDraw.forEach((line) => {
      if (line.points.length < 2) return; // Need at least one point (2 coords)

      switch (line.type) {
        case PenType.PEN:
          drawNormalPenPolyline(ctx, line);
          break;
        case PenType.LINE:
          drawStraightLine(ctx, line);
          break;
        case PenType.HIGHLIGHT_PEN:
          drawHighlightPolyline(ctx, line);
          break;
        case PenType.HIGHLIGHT_PEN_STRAIGHT:
          drawStraightLine(ctx, line, true);
          break;
        case PenType.DOTTED_LINE:
          drawDashLine(ctx, line);
          break;
        case PenType.ERASER:
          drawEraserLine(ctx, line);
          break;
        default:
          console.warn("Unknown pen type:", line.type);
      }
    });
  }, [penLinesToDraw, pageSize, pageScale, currentFileId]);

  return (
    <div style={{ width: "100%", height: "100%", position: "relative" }}>
      <canvas ref={canvasRef} style={{ display: "block" }} />
    </div>
  );
};

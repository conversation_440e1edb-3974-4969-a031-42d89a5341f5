import { toNumber } from "lodash";
import React, {
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
} from "react";
import { createPortal } from "react-dom";
import { useEventListener, useLongPress, useThrottleFn } from "ahooks";
import {
  ILayerScale,
  ILayerSize,
  usePdfInfoStore,
} from "@/store/pdf-info-store";
import { useRectToolboxStore } from "@/store/rect-toolbox-store";
import { usePinchZoomStore } from "@/store/pinch-zoom-store";
import { LAYER_TYPE, RECT_ACTION_TYPE } from "@/constant";
import { IOcrRect, IOcrRectRaw, IRectAnnotation } from "@/db/app-db";
import { getOcrDataByFileUid } from "@/db/ocr-data-hooks";
import {
  deleteRectsByUIDList,
  saveRects,
  useGetPageRects,
} from "@/db/rect-data-hooks";
import { createNoteData, deleteNoteByUid } from "@/db/note-data-hooks";
import { useShowResultRect } from "@/db/config-data-hooks";
import * as BoxHelperJS from "./box-helper-js";
import * as BoxHelperWASM from "./box-helper-wasm";
import type { UnifiedBox, UnifiedLine } from "./box-types";
import {
  colorRgbToRgba,
  isPointInCircle,
  isPointInRect,
  drawRect,
  drawCircle,
  drawText,
  drawLine,
} from "./helper";
import type { CanvasPosition } from "./helper";
import FloatToolbox from "@/component/word-toolbox";

type DrawEvent = TouchEvent | MouseEvent;

interface CanvasSelectProps {
  file: File;
  pageIndex: number;
  pageSize: ILayerSize;
  imageSize: ILayerSize;
  pageScale: ILayerScale;
  onLoadingFinished: () => void;
}

export type OcrDrawRect = {
  x: number;
  y: number;
  width: number;
  height: number;
  fill: string;
  stroke?: string;
  strokeWidth?: number;
};

type OcrEngine = "JS" | "WASM";

export const CanvasSelect = (props: CanvasSelectProps) => {
  const { pageIndex, pageSize, imageSize, pageScale, onLoadingFinished } =
    props;
  const [currentFileId] = usePdfInfoStore((state) => [state.currentFileId]);
  const [activePageNumber, setActivePageNumber] = usePdfInfoStore((state) => [
    state.activePageNumber,
    state.setActivePageNumber,
  ]);
  const [activeLayer] = usePdfInfoStore((state) => [state.activeLayer]);
  const [zoomScale] = useRectToolboxStore((state) => [state.zoomScale]);
  const [enableDraggable, disableDraggable] = usePinchZoomStore((state) => [
    state.enableDraggable,
    state.disableDraggable,
  ]);

  const showResultRect = useShowResultRect();

  const [loading, setLoading] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const currentClickRectIndex = useRef(-1);
  const currentClickStagePosition = useRef({ x: 0, y: 0 });

  const annotations = useGetPageRects(currentFileId, pageIndex);
  const [unifiedBoxes, setUnifiedBoxes] = useState<UnifiedBox[]>([]);
  const [unifiedLines, setUnifiedLines] = useState<UnifiedLine[]>([]);
  const ocrEngineType = useRef<OcrEngine>("JS");
  const isDrawing = useRef(false);
  const drawingStartPosition = useRef({ x: 0, y: 0 });
  const isDrawingStartPositionExceed = useRef(false);
  const drawingDirection = useRef<"left" | "right" | "">("");
  const [firstSelectedBox, setFirstSelectedBox] = useState<
    UnifiedBox | undefined
  >();
  const [lastSelectedBox, setLastSelectedBox] = useState<
    UnifiedBox | undefined
  >();
  const [toolboxKey, setToolboxKey] = useState(0);

  function joinSentences(boxes: UnifiedBox[]) {
    if (ocrEngineType.current === "WASM") {
      return BoxHelperWASM.joinSentences(boxes);
    } else {
      return BoxHelperJS.joinSentences(boxes);
    }
  }

  function joinOriginalSentences(
    unifiedBoxes: UnifiedBox[],
    selectedBoxes: UnifiedBox[]
  ) {
    if (ocrEngineType.current === "WASM") {
      return BoxHelperWASM.joinOriginalSentences(unifiedBoxes, selectedBoxes);
    } else {
      return BoxHelperJS.joinOriginalSentences(unifiedBoxes, selectedBoxes);
    }
  }

  const annotationsToDraw = useMemo(() => {
    return annotations
      .filter((rect) => rect.display)
      .sort((a, b) => {
        // 面积大的排在前面
        return b.width * b.height - a.width * a.height;
      });
  }, [annotations]);

  const annotationsTagToDraw = useMemo(() => {
    const middleBoxes = annotations
      .filter((rect) => !rect.display && rect.wordBoxes?.length)
      .sort((a, b) => {
        // 按uid排序，uid小的排前面
        return a.uid - b.uid;
      })
      .map((rect) => {
        const middleBoxUid =
          rect.wordBoxes?.[Math.floor((rect.wordBoxes.length || 0) / 2)];
        const box = unifiedBoxes.find((box) => box.uid === middleBoxUid);
        const firstChildRect = annotations.find(
          (r) => r.parentUid === rect.uid
        );
        return box ? { ...box, color: firstChildRect?.color } : undefined;
      })
      .filter((box) => !!box);

    const fontSize =
      middleBoxes.length > 0
        ? Math.max(
            ...middleBoxes.map((box) =>
              Math.abs(box.rect.bottom - box.rect.top)
            )
          )
        : 12;

    return middleBoxes.map((box, index) => ({
      x: (box.rect.left + box.rect.right) / 2,
      y: box.rect.top - fontSize,
      fontSize: fontSize,
      fill: colorRgbToRgba(box.color) ?? colorRgbToRgba("rgb(255, 255, 255)"),
      text: `${index + 1}`,
    }));
  }, [annotations, unifiedBoxes]);

  const wordBoxes: OcrDrawRect[] = useMemo(() => {
    return unifiedBoxes.map((box) => {
      return {
        x: box.rect.left,
        y: box.rect.top,
        width: Math.abs(box.rect.right - box.rect.left),
        height: Math.abs(box.rect.bottom - box.rect.top),
        fill: "transparent",
        stroke: showResultRect ? "red" : undefined,
        strokeWidth: showResultRect ? 1 : undefined,
      };
    });
  }, [unifiedBoxes, showResultRect]);

  const selectedBoxes = useMemo(() => {
    if (!firstSelectedBox || !lastSelectedBox) {
      return [];
    }
    let firstIndex = unifiedBoxes.indexOf(firstSelectedBox);
    let lastIndex = unifiedBoxes.indexOf(lastSelectedBox);

    // 返回fistIndex和lastIndex之间的boxes，包括fistIndex和lastIndex
    return unifiedBoxes.slice(
      Math.min(firstIndex, lastIndex),
      Math.max(firstIndex, lastIndex) + 1
    );
  }, [firstSelectedBox, lastSelectedBox, unifiedBoxes]);

  const selectedBoxesContainerRects: OcrDrawRect[] = useMemo(() => {
    // 按lineIndex分组，根据每一组的left, right, top, bottom生成每一组的rect
    return selectedBoxes
      .reduce<UnifiedBox[][]>((acc, box) => {
        const lineIndex = box.lineIndex;
        if (!acc[lineIndex]) {
          acc[lineIndex] = [];
        }
        acc[lineIndex].push(box);
        return acc;
      }, [])
      .filter((boxes) => boxes.length > 0) // 过滤掉空的rects数组，因为空的rects数组无法生成rect
      .map((boxes) => ({
        left: Math.min(...boxes.map((box) => box.rect.left)),
        right: Math.max(...boxes.map((box) => box.rect.right)),
        top: Math.min(...boxes.map((box) => box.rect.top)),
        bottom: Math.max(...boxes.map((box) => box.rect.bottom)),
      }))
      .map((rect) => ({
        x: rect.left,
        y: rect.top,
        width: Math.abs(rect.right - rect.left),
        height: Math.abs(rect.bottom - rect.top),
        fill: "rgba(21, 126, 251, 0.6)",
      }));
  }, [selectedBoxes]);

  const selectedBoxesContainerRectsMiddleWidth = useMemo(() => {
    const totalWidth = selectedBoxesContainerRects.reduce(
      (acc, rect) => acc + rect.width,
      0
    );

    return totalWidth / 2;
  }, [selectedBoxesContainerRects]);

  // 切换其他图层时，取消激活
  useEffect(() => {
    if (activeLayer !== LAYER_TYPE.RECT || activePageNumber !== pageIndex) {
      setFirstSelectedBox(undefined);
      setLastSelectedBox(undefined);
    }
  }, [activeLayer, activePageNumber, pageIndex]);

  useEffect(() => {
    if (imageSize.width === 0 || imageSize.height === 0) {
      return;
    }

    init();
  }, [imageSize]);

  useEffect(() => {
    renderCanvas();
  }, [
    wordBoxes,
    annotationsToDraw,
    annotationsTagToDraw,
    selectedBoxesContainerRects,
    firstSelectedBox,
    lastSelectedBox,
    drawingDirection.current,
    pageScale,
    zoomScale,
  ]);

  async function init() {
    const fileUid = (props.file as any).uid;
    if (!fileUid) return;
    const ocrData = await getOcrDataByFileUid(toNumber(fileUid));
    if (ocrData) {
      const scaleX = imageSize.width / ocrData.width;
      const scaleY = imageSize.height / ocrData.height;

      let words: IOcrRectRaw[] | IOcrRect[] = [];
      if (ocrData.data) {
        // wasm处理的ocr数据里没有line属性
        const isWASMEngine =
          ocrData.data.length > 0 && ocrData.data.every((d) => !d.line);
        ocrEngineType.current = isWASMEngine ? "WASM" : "JS";

        if (isWASMEngine) {
          words = ocrData.data.map((w) => ({
            uid: w.uid,
            text: w.text,
            confidence: w.confidence,
            rect: {
              left: w.bbox.x0,
              right: w.bbox.x1,
              top: w.bbox.y0,
              bottom: w.bbox.y1,
            },
          }));
        } else {
          words = ocrData.data;
        }
      }

      if (ocrEngineType.current === "WASM") {
        const ocrRects = words.filter(
          (word): word is IOcrRect => "rect" in word
        );
        const boxes = BoxHelperWASM.groupBoxesByLine(ocrRects, scaleX, scaleY);

        setUnifiedBoxes(boxes);
      } else {
        const rawWords = words.filter(
          (word): word is IOcrRectRaw => "bbox" in word
        );
        const { boxes, lines } = BoxHelperJS.unifyingBoxes(
          rawWords,
          scaleX,
          scaleY
        );

        setUnifiedBoxes(boxes);
        setUnifiedLines(lines);
      }

      setLoading(false);
      onLoadingFinished();
    }
  }

  function renderCanvas() {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Apply scaling
    ctx.save();
    ctx.scale(pageScale.x, pageScale.y);

    // Draw word boxes
    for (const rect of wordBoxes) {
      drawRect(ctx, rect);
    }

    // Draw annotations
    for (const rect of annotationsToDraw) {
      drawRect(ctx, {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
        fill: colorRgbToRgba(rect.color),
        stroke: "transparent",
      });
    }

    // // Draw annotation tags
    // for (const rect of annotationsTagToDraw) {
    //   drawText(ctx, rect);
    //   drawCircle(ctx, {
    //     x: rect.x + rect.fontSize / 3,
    //     y: rect.y + rect.fontSize / 3,
    //     radius: rect.fontSize / 2,
    //     stroke: rect.fill,
    //     fill: "transparent",
    //   });
    // }

    // Draw selected box container rects
    for (const rect of selectedBoxesContainerRects) {
      drawRect(ctx, rect);
    }

    // Draw anchors
    if (selectedBoxesContainerRects.length > 0) {
      const firstRect = selectedBoxesContainerRects[0];
      const lastRect =
        selectedBoxesContainerRects[selectedBoxesContainerRects.length - 1];

      // Draw first anchor if not drawing left
      if (firstRect && drawingDirection.current !== "left") {
        // Circle
        drawCircle(ctx, {
          x: firstRect.x,
          y: firstRect.y - 8 - 2,
          radius: 8,
          fill: "white",
          stroke: "#409EFF",
        });

        // Line
        drawLine(ctx, {
          points: [
            firstRect.x,
            firstRect.y - 2,
            firstRect.x,
            firstRect.y + firstRect.height,
          ],
          stroke: "#409EFF",
          fill: "#409EFF",
          width: 2,
        });

        // Invisible circle for easier interaction (handled in hit testing)
      }

      // Draw last anchor if not drawing right
      if (lastRect && drawingDirection.current !== "right") {
        // Circle
        drawCircle(ctx, {
          x: lastRect.x + lastRect.width,
          y: lastRect.y + lastRect.height + 8 + 2,
          radius: 8,
          fill: "white",
          stroke: "#409EFF",
        });

        // Line
        drawLine(ctx, {
          points: [
            lastRect.x + lastRect.width,
            lastRect.y,
            lastRect.x + lastRect.width,
            lastRect.y + lastRect.height + 2,
          ],
          stroke: "#409EFF",
          fill: "#409EFF",
          width: 2,
        });

        // Invisible circle for easier interaction (handled in hit testing)
      }
    }

    ctx.restore();
  }

  const getCanvasPosition = useCallback(
    (event: DrawEvent): CanvasPosition | null => {
      const canvas = canvasRef.current;
      if (!canvas) return null;

      const rect = canvas.getBoundingClientRect();
      let clientX, clientY;

      if (event instanceof MouseEvent) {
        clientX = event.clientX;
        clientY = event.clientY;
      } else if (event instanceof TouchEvent) {
        if (event.touches.length === 0) return null;
        clientX = event.touches[0]!.clientX;
        clientY = event.touches[0]!.clientY;
      } else {
        return null;
      }

      // Get position relative to canvas
      const x = (clientX - rect.left) / pageScale.x / zoomScale;
      const y = (clientY - rect.top) / pageScale.y / zoomScale;

      return { x, y };
    },
    [pageScale.x, pageScale.y, zoomScale]
  );

  // 点击空白处取消选择
  function handleCanvasClick(e: DrawEvent) {
    if (isDrawing.current) {
      return;
    }

    setActivePageNumber(pageIndex);
    const pos = getCanvasPosition(e);
    if (!pos) {
      return;
    }

    currentClickStagePosition.current = pos;

    if (selectedBoxesContainerRects.length > 0) {
      const firstRect = selectedBoxesContainerRects[0];
      const lastRect =
        selectedBoxesContainerRects[selectedBoxesContainerRects.length - 1];

      // Check if clicked on first anchor
      if (firstRect && drawingDirection.current !== "left") {
        const firstAnchorCircle = {
          x: firstRect.x,
          y: firstRect.y - 8 - 2,
          radius: 16, // Larger radius for easier interaction
        };

        if (isPointInCircle(pos, firstAnchorCircle)) {
          handleBoxAnchorClick(e, "left");
          return;
        }
      }

      if (lastRect && drawingDirection.current !== "right") {
        // Check if clicked on last anchor
        const lastAnchorCircle = {
          x: lastRect.x + lastRect.width,
          y: lastRect.y + lastRect.height + 8 + 2,
          radius: 16, // Larger radius for easier interaction
        };

        if (isPointInCircle(pos, lastAnchorCircle)) {
          handleBoxAnchorClick(e, "right");
          return;
        }
      }
    }

    // Check if clicked on a selected box container
    if (selectedBoxesContainerRects.length > 0) {
      for (const rect of selectedBoxesContainerRects) {
        if (isPointInRect(pos, rect)) {
          handleContainerRectClick(e);
          return;
        }
      }
    }

    // Check if clicked on an annotation
    for (const rect of annotationsToDraw) {
      if (isPointInRect(pos, rect)) {
        handleRectClick(rect);
        return;
      }
    }
  }

  useLongPress(
    (evt) => {
      if (isDrawing.current) {
        return;
      }

      if (currentClickRectIndex.current === -1) {
        const pos = currentClickStagePosition.current;
        let selectedBox = null;
        let minDistance = Infinity;
        for (let box of unifiedBoxes) {
          const centerX = (box.rect.left + box.rect.right) / 2;
          const centerY = (box.rect.top + box.rect.bottom) / 2;
          const distance = Math.sqrt(
            Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
          );
          if (distance < minDistance) {
            minDistance = distance;
            selectedBox = box;
          }
        }

        if (!selectedBox) {
          setFirstSelectedBox(undefined);
          setLastSelectedBox(undefined);
        } else {
          setFirstSelectedBox(selectedBox);
          setLastSelectedBox(selectedBox);
          if (
            selectedBox.rect.left < pos.x &&
            selectedBox.rect.right > pos.x &&
            selectedBox.rect.top < pos.y &&
            selectedBox.rect.bottom > pos.y
          ) {
            handleTriggerAnchorClick(evt, "right");
          }
        }
      } else {
        const parentRect = annotations.find(
          (rect) => rect.uid === currentClickRectIndex.current
        );
        if (parentRect) {
          const firstBoxUid = parentRect.wordBoxes?.[0];
          const lastBoxUid =
            parentRect.wordBoxes?.[parentRect.wordBoxes.length - 1];
          const firstBox = unifiedBoxes.find((box) => box.uid === firstBoxUid);
          const lastBox = unifiedBoxes.find((box) => box.uid === lastBoxUid);
          if (firstBox && lastBox) {
            setFirstSelectedBox(firstBox);
            setLastSelectedBox(lastBox);
          }
        }
      }
    },
    canvasRef,
    {
      onClick: () => {
        if (isDrawing.current) {
          return;
        }

        setActivePageNumber(pageIndex);
        if (firstSelectedBox && lastSelectedBox) {
          setFirstSelectedBox(undefined);
          setLastSelectedBox(undefined);
          currentClickRectIndex.current = -1;
        }
      },
      moveThreshold: { x: 10 * zoomScale, y: 10 * zoomScale },
      delay: 200,
    }
  );

  function handleTriggerAnchorClick(
    evt: DrawEvent,
    direction: "left" | "right"
  ) {
    setActivePageNumber(pageIndex);

    const pos = currentClickStagePosition.current;
    if (!pos) {
      return;
    }

    isDrawing.current = true;
    drawingStartPosition.current = pos;
    drawingDirection.current = direction;

    setToolboxKey((prev) => prev + 1);
    if (evt.cancelable) {
      evt.preventDefault();
    }
    disableDraggable();
  }

  function handleCanvasMove(e: DrawEvent) {
    if (!isDrawing.current) {
      return;
    }

    const pos = getCanvasPosition(e);
    if (!pos) {
      return;
    }

    // 当移动距离超过一定值时，认为是有效操作
    if (!isDrawingStartPositionExceed.current) {
      if (
        Math.abs(pos.x - drawingStartPosition.current.x) >
          10 / pageScale.x / zoomScale ||
        Math.abs(pos.y - drawingStartPosition.current.y) >
          10 / pageScale.y / zoomScale
      ) {
        isDrawingStartPositionExceed.current = true;
        throttledHandleMouseMove(pos);
      }
    } else {
      throttledHandleMouseMove(pos);
    }
  }

  const { run: throttledHandleMouseMove } = useThrottleFn(
    (pos: { x: number; y: number }) => {
      let selectedBox = null;
      let minDistance = Infinity;
      for (let box of unifiedBoxes) {
        const centerX = (box.rect.left + box.rect.right) / 2;
        const centerY = (box.rect.top + box.rect.bottom) / 2;
        const distance = Math.sqrt(
          Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
        );
        if (
          distance < minDistance &&
          ((drawingDirection.current === "left" && centerY >= pos.y) ||
            (drawingDirection.current === "right" && centerY <= pos.y))
        ) {
          minDistance = distance;
          selectedBox = box;
        }
      }

      if (selectedBox) {
        if (
          drawingDirection.current === "left" &&
          lastSelectedBox &&
          selectedBox.uid <= lastSelectedBox.uid
        ) {
          setFirstSelectedBox(selectedBox);
        } else if (
          drawingDirection.current === "right" &&
          firstSelectedBox &&
          selectedBox.uid >= firstSelectedBox?.uid
        ) {
          setLastSelectedBox(selectedBox);
        }
      }
    },
    { wait: 16, leading: false, trailing: true }
  );

  function handleCanvasEnd() {
    isDrawing.current = false;
    currentClickRectIndex.current = -1;
    drawingDirection.current = "";
    isDrawingStartPositionExceed.current = false;
    setToolboxKey((prev) => prev + 1);
    enableDraggable();
  }

  function handleContainerRectClick(evt: DrawEvent) {
    setActivePageNumber(pageIndex);

    const pos = getCanvasPosition(evt);
    if (!pos) {
      return;
    }

    // 对pos在selectedBoxesContainerRects中的位置进行判断
    // 根据累加的x坐标判断其是否在中间位置，y坐标只用来确认其所在的rect
    const posAccWidth = selectedBoxesContainerRects.reduce((acc, rect) => {
      const isInRect = rect.y <= pos.y && rect.y + rect.height >= pos.y;
      if (isInRect) {
        return acc + pos.x - rect.x;
      }
      return acc + rect.width;
    }, 0);

    isDrawing.current = true;
    drawingStartPosition.current = pos;
    drawingDirection.current =
      posAccWidth > selectedBoxesContainerRectsMiddleWidth ? "right" : "left";

    setToolboxKey((prev) => prev + 1);
    if (evt.cancelable) {
      evt.preventDefault();
    }
    disableDraggable();
  }

  function handleBoxAnchorClick(evt: DrawEvent, direction: "left" | "right") {
    setActivePageNumber(pageIndex);

    const pos = getCanvasPosition(evt);
    if (!pos) {
      return;
    }

    isDrawing.current = true;
    drawingStartPosition.current = pos;
    drawingDirection.current = direction;

    setToolboxKey((prev) => prev + 1);
    if (evt.cancelable) {
      evt.preventDefault();
    }
    disableDraggable();
  }

  function handleRectClick(rect: IRectAnnotation) {
    const parentRect = annotations.find((r) => r.uid === rect.parentUid);
    if (parentRect) {
      currentClickRectIndex.current = parentRect.uid;
    } else {
      currentClickRectIndex.current = -1;
    }
  }

  function handleWordCopy() {
    const sentence = joinSentences(selectedBoxes);
    navigator.clipboard?.writeText(sentence);
    setFirstSelectedBox(undefined);
    setLastSelectedBox(undefined);
  }

  async function handleWordAddToNote() {
    const sentence = joinSentences(selectedBoxes);

    const [prefixSentence, suffixSentence] = joinOriginalSentences(
      unifiedBoxes,
      selectedBoxes
    );

    const selectedBoxesUid = selectedBoxes.map((box) => box.uid);
    // 查找是否有包含当前选中的box的rect, wordBoxes要和selectedBoxesUid一致
    const annotation = annotations.find(
      (rect) =>
        rect.wordBoxes &&
        rect.wordBoxes.length === selectedBoxesUid.length &&
        rect.wordBoxes.every((uid) => selectedBoxesUid.includes(uid))
    );
    if (annotation) {
      const data = {
        layerType: LAYER_TYPE.RECT,
        type: RECT_ACTION_TYPE.DISPLAY,
        rect: annotation,
        text: sentence,
      };
      window.parent?.postMessage(data, "*");
      return;
    }

    const annotationToAdd = {
      fileId: currentFileId,
      uid: Date.now(),
      pageIndex: pageIndex,
      display: false,
      x: Math.min(...selectedBoxesContainerRects.map((rect) => rect.x)),
      y: Math.min(...selectedBoxesContainerRects.map((rect) => rect.y)),
      width:
        Math.max(
          ...selectedBoxesContainerRects.map((rect) => rect.x + rect.width)
        ) - Math.min(...selectedBoxesContainerRects.map((rect) => rect.x)),
      height:
        Math.max(
          ...selectedBoxesContainerRects.map((rect) => rect.y + rect.height)
        ) - Math.min(...selectedBoxesContainerRects.map((rect) => rect.y)),
      color: "transparent",
      wordBoxes: selectedBoxes.map((box) => box.uid),
    };

    await saveRects(annotationToAdd);
    await createNoteData(
      currentFileId,
      annotationToAdd.uid,
      sentence,
      prefixSentence ?? "",
      suffixSentence ?? "",
      "rgb(21, 126, 251)"
    );

    // 显示用rect
    const showingRectsToAdd = selectedBoxesContainerRects.map(
      (rect, index) => ({
        fileId: currentFileId,
        uid: Date.now() + index,
        pageIndex: pageIndex,
        display: true,
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
        color: "rgb(21, 126, 251)",
        parentUid: annotationToAdd.uid,
      })
    );
    await saveRects(showingRectsToAdd);

    // 发送消息
    const data = {
      layerType: LAYER_TYPE.RECT,
      type: RECT_ACTION_TYPE.DISPLAY,
      rect: annotationToAdd,
      text: sentence,
      isNew: true,
    };
    window.parent?.postMessage(data, "*");
  }

  function handleWordSpeak() {
    const sentence = joinSentences(selectedBoxes);
    speechSynthesis?.speak(new SpeechSynthesisUtterance(sentence));
  }

  function handleWordTranslate() {
    const sentence = joinSentences(selectedBoxes);
    const data = {
      layerType: LAYER_TYPE.RECT,
      type: RECT_ACTION_TYPE.RESEARCH,
      text: sentence,
      uid: selectedBoxes[0]?.uid,
    };
    window.parent?.postMessage(data, "*");
  }

  function handleWordDelete(uid: number) {
    const annotation = annotations.find((rect) => rect.uid === uid);
    if (!annotation) {
      return;
    }

    const children = annotations.filter(
      (rect) => rect.parentUid === annotation.uid
    );
    const ids = [annotation.uid, ...children.map((rect) => rect.uid)];
    deleteRectsByUIDList(ids);
    deleteNoteByUid(annotation.uid);

    const data = {
      layerType: LAYER_TYPE.RECT,
      type: RECT_ACTION_TYPE.DELETE,
    };
    window.parent?.postMessage(data, "*");

    setFirstSelectedBox(undefined);
    setLastSelectedBox(undefined);
  }

  useEventListener("touchstart", handleCanvasClick, {
    target: canvasRef,
    passive: false,
  });
  useEventListener("touchmove", handleCanvasMove, {
    target: canvasRef,
    passive: false,
  });
  useEventListener("mousedown", handleCanvasClick, {
    target: canvasRef,
    passive: false,
  });
  useEventListener("mousemove", handleCanvasMove, {
    target: canvasRef,
    passive: false,
  });

  useEventListener("mouseup", handleCanvasEnd, { target: window });
  useEventListener("touchend", handleCanvasEnd, { target: window });

  useEventListener("message", (e: MessageEvent) => {
    const { layerType, type, uid } = e.data;
    if (layerType === LAYER_TYPE.RECT && type === RECT_ACTION_TYPE.ADD_NOTE) {
      if (selectedBoxes.length > 0 && uid === selectedBoxes[0]?.uid) {
        handleWordAddToNote();
      }
    } else if (
      layerType === LAYER_TYPE.RECT &&
      type === RECT_ACTION_TYPE.DELETE
    ) {
      handleWordDelete(uid);
    }
  });

  if (loading) {
    return (
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-base-100/80 z-50">
        <span className="loading loading-spinner loading-lg text-primary mb-2"></span>
      </div>
    );
  }

  return (
    <div style={{ width: "100%", height: "100%", position: "relative" }}>
      {createPortal(
        <FloatToolbox
          key={toolboxKey}
          isDrawing={isDrawing}
          selectedBoxesContainerRects={selectedBoxesContainerRects}
          canvasRef={canvasRef}
          pageScaleX={pageScale.x}
          pageScaleY={pageScale.y}
          onWordCopy={handleWordCopy}
          onWordAddToCard={handleWordAddToNote}
          onWordSpeak={handleWordSpeak}
          onWordTranslate={handleWordTranslate}
        />,
        document.body
      )}
      <canvas
        ref={canvasRef}
        width={pageSize.width}
        height={pageSize.height}
        style={{
          width: `${pageSize.width}px`,
          height: `${pageSize.height}px`,
        }}
      />
    </div>
  );
};

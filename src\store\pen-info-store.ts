import { create } from "zustand";

export enum PenType {
  HAND = "手型",
  PEN = "普通笔",
  HIGHLIGHT_PEN = "荧光笔",
  HIGHLIGHT_PEN_STRAIGHT = "直线荧光笔",
  LINE = "直线笔",
  DOTTED_LINE = "虚线笔",
  ERASER = "橡皮擦",
}

interface PenStates {
  penType: PenType;
  setPenType: (type: PenType) => void;
  penColor: string;
  setPenColor: (color: string) => void;
  penWidth: number;
  setPenWidth: (width: number) => void;
}

export const usePenStates = create<PenStates>((set) => ({
  penType: PenType.PEN,
  setPenType: (type) => set({ penType: type }),
  penColor: "rgba(0, 0, 0, 1)",
  setPenColor: (color) => set({ penColor: color }),
  penWidth: 1,
  setPenWidth: (width) => set({ penWidth: width }),
}));

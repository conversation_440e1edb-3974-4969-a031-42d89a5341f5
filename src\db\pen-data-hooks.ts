import { useLiveQuery } from "dexie-react-hooks";
import { IPenLine, appDbInstance } from "@/db/app-db";

const db = appDbInstance;

export function savePenLines(lines: IPenLine[] | IPenLine) {
  if (Array.isArray(lines)) {
    db.penData.bulkPut(lines);
  } else {
    db.penData.put(lines);
  }
}

export function useGetPagePenLines(fileId: number, pageIndex: number) {
  const list = useLiveQuery(async () => {
    return db.penData
      .where({
        fileId: fileId,
        pageIndex: pageIndex,
      })
      .toArray();
  }, [fileId, pageIndex]);
  return list ? list : [];
}

import React, { useEffect, useState } from "react";
import { appDbInstance } from "@/db/app-db";
import ImageContainer from "@/component/image-container/index";

type MultiImagesContainerProps = {
  files: File[];
  onLoadSuccess: () => void;
};

export default function MultiImagesContainer(props: MultiImagesContainerProps) {
  const { files, onLoadSuccess } = props;
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    await appDbInstance.open();
    setLoading(false);
  };

  if (loading) {
    return (
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-base-100/80 z-50">
        <span className="loading loading-spinner loading-lg text-primary mb-2"></span>
        数据获取中
      </div>
    );
  }

  return (
    <>
      {files.map((item, index) => (
        <ImageContainer
          key={index}
          index={index}
          totalPage={files.length}
          file={item}
          instantRender={true}
          onLoadSuccess={onLoadSuccess}
        />
      ))}
    </>
  );
}

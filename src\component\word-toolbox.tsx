import React, { useEffect, useMemo } from "react";
import { Button } from "react-daisyui";
import { RECT_ACTION_TYPE, LAYER_TYPE } from "@/constant";
import { useRectToolboxStore } from "@/store/rect-toolbox-store";
import { OcrDrawRect } from "@/layers/canvas-select";

type WordFloatToolboxProps = {
  isDrawing: React.RefObject<boolean>;
  selectedBoxesContainerRects: OcrDrawRect[];
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  pageScaleX: number;
  pageScaleY: number;
  // 复制
  onWordCopy?: () => void;
  // 加入学习卡
  onWordAddToCard?: () => void;
  // 朗读
  onWordSpeak?: () => void;
  // 研究
  onWordTranslate?: () => void;
  // 下划线
  onWordUnderline?: () => void;
  // 删除
  onWordDelete?: () => void;
};

const WordFloatToolbox: React.FC<WordFloatToolboxProps> = (props) => {
  const {
    isDrawing,
    canvasRef,
    selectedBoxesContainerRects,
    pageScaleX,
    pageScaleY,
  } = props;
  const { onWordCopy, onWordAddToCard, onWordTranslate } = props;
  const [zoomScale] = useRectToolboxStore((state) => [state.zoomScale]);

  const showToolbox = useMemo(() => {
    return selectedBoxesContainerRects.length > 0 && !isDrawing.current;
  }, [selectedBoxesContainerRects, isDrawing.current]);

  const cardPositionStyle = useMemo(() => {
    const cardWidth: number = 224;
    const cardHeight: number = 40;

    const firstRect = selectedBoxesContainerRects[0];
    const lastRect =
      selectedBoxesContainerRects[selectedBoxesContainerRects.length - 1];
    if (!firstRect || !lastRect) return {};

    const canvasRect = canvasRef.current?.getBoundingClientRect();
    if (!canvasRect) return {};

    const screenWidth =
      window.innerWidth ||
      document.documentElement.clientWidth ||
      document.body.clientWidth;
    const screenHeight =
      window.innerHeight ||
      document.documentElement.clientHeight ||
      document.body.clientHeight;

    // 将card放置在第一个选中的框的上方
    let cardX: number =
      canvasRect.left +
      firstRect.x * pageScaleX * zoomScale +
      (firstRect.width * pageScaleX * zoomScale) / 2 -
      cardWidth / 2;

    if (zoomScale !== 1) {
      // 缩放状态限制card在屏幕内
      if (cardX < 0) {
        cardX = 0;
      } else if (cardX + cardWidth > screenWidth) {
        cardX = screenWidth - cardWidth;
      }
    } else {
      if (cardX + cardWidth > canvasRect.right) {
        cardX = canvasRect.right - cardWidth;
      } else if (cardX < canvasRect.left) {
        cardX = canvasRect.left;
      }
    }

    let cardY: number =
      canvasRect.top +
      window.scrollY +
      firstRect.y * pageScaleY * zoomScale -
      cardHeight -
      20 * zoomScale;

    if (zoomScale !== 1) {
      // 缩放状态限制card在屏幕内
      if (cardY < 0) {
        cardY =
          canvasRect.top +
          window.scrollY +
          lastRect.y * pageScaleY * zoomScale +
          lastRect.height * pageScaleY * zoomScale +
          20 * zoomScale;
      }
    } else {
      if (cardY < canvasRect.top) {
        cardY =
          canvasRect.top +
          window.scrollY +
          lastRect.y * pageScaleY * zoomScale +
          lastRect.height * pageScaleY * zoomScale +
          20 * zoomScale;
      }
    }

    return {
      left: `${cardX}px`,
      top: `${cardY}px`,
    };
  }, [selectedBoxesContainerRects, pageScaleX, pageScaleY, zoomScale]);

  useEffect(() => {
    const dataToSend = {
      layerType: LAYER_TYPE.RECT,
      type: RECT_ACTION_TYPE.SHOW_TOOLBOX,
      showToolbox,
    };
    window.parent?.postMessage(dataToSend, "*");
  }, [showToolbox]);

  return (
    showToolbox && (
      <div
        className="absolute p-0"
        style={{ ...cardPositionStyle, zIndex: 200 }}
      >
        <div className="card card-compact bg-base-200 shadow-xl">
          <div className="flex justify-center items-center p-1">
            <Button
              className="btn-ghost p-1"
              size="sm"
              onTouchStart={onWordTranslate}
            >
              翻译
            </Button>
            <Button
              className="btn-ghost p-1"
              size="sm"
              onTouchStart={onWordAddToCard}
            >
              笔记
            </Button>
            <Button
              className="btn-ghost p-1"
              size="sm"
              onTouchStart={onWordCopy}
            >
              复制
            </Button>
          </div>
        </div>
      </div>
    )
  );
};

export default WordFloatToolbox;

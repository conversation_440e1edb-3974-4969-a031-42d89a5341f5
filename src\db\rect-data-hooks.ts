import { useLiveQuery } from "dexie-react-hooks";
import { IRectAnnotation, appDbInstance } from "./app-db";

const db = appDbInstance;

export function saveRects(rects: IRectAnnotation[] | IRectAnnotation) {
  if (Array.isArray(rects)) {
    return db.rectData.bulkPut(rects);
  } else {
    return db.rectData.put(rects);
  }
}

export function useGetPageRects(fileId: number, pageIndex: number) {
  const list = useLiveQuery(
    async () => db.rectData.where({ fileId, pageIndex }).toArray(),
    [fileId, pageIndex]
  );

  return list ? list : [];
}

export function getRectByUID(uid?: number) {
  return db.rectData.where({ uid }).first();
}

export function deleteRectByUID(uid: number) {
  return db.rectData.delete(uid);
}

export function deleteRectsByUIDList(uidList: number[]) {
  return db.rectData.bulkDelete(uidList);
}

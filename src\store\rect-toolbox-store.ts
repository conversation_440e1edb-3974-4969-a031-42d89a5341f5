import {create} from "zustand";
import {IRectAnnotation} from "@/db/app-db";

type ToolboxPosition = {
  x: number;
  y: number;
  clientX: number;
  clientY: number;
}

interface RectToolboxStore {
  activeRect: IRectAnnotation | null,
  setActiveRect: (rect: IRectAnnotation | null) => void,
  toolboxPosition: ToolboxPosition,
  setToolboxPosition: (position: ToolboxPosition) => void,
  scrollRectUid: number,
  setScrollRectUid: (uid: number) => void,
  zoomScale: number,
  setZoomScale: (scale: number) => void,
}

export const useRectToolboxStore = create<RectToolboxStore>((set, get) => ({
  activeRect: null,
  setActiveRect: (rect) => set({activeRect: rect}),
  toolboxPosition: {x: 0, y: 0, clientX: 0, clientY: 0},
  setToolboxPosition: (position) => set({toolboxPosition: position}),
  scrollRectUid: 0,
  setScrollRectUid: (uid) => set({scrollRectUid: uid}),
  zoomScale: 1,
  setZoomScale: (scale) => set({zoomScale: scale}),
}));

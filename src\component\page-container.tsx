import React, {useEffect, useRef, useState} from "react";
import {PDFPageProxy} from "pdfjs-dist";
import {Page} from "react-pdf";
import {useSize} from "ahooks";
import {isFinite, isNumber} from "lodash";
import {LAYER_TYPE, RENDER_SCALE} from "@/constant";
import {DrawPen} from "@/layers/draw-pen";
import {DrawRect} from "@/layers/draw-rect";
import {ILayerScale, ILayerSize, usePdfInfoStore} from "@/store/pdf-info-store";
import {sendPdfCoverData} from "@/lib/app-layer-message";

interface PageContainerProps {
  index: number,
  onPdfLoadSuccess: (index: number) => void
}

export default function PageContainer(props: PageContainerProps) {
  const [activeLayer] = usePdfInfoStore(state => [state.activeLayer]);
  const [addPdfInfo] = usePdfInfoStore(state => [state.addPdfInfo]);
  const pageCanvasRef = useRef<HTMLCanvasElement>();
  const documentRef = useRef<HTMLDivElement>(null);
  const documentSize = useSize(documentRef);
  const [elementSize, setElementSize] = useState<ILayerSize>({width: 0, height: 0});
  const [pdfPageInfo, setPdfPageInfo] = useState<PDFPageProxy>();
  const [pageScale, setPageScale] = useState<ILayerScale>({x: 1, y: 1});

  // 缩放控制
  useEffect(() => {
    setElementSize({width: documentSize?.width || 0, height: documentSize?.height || 0});
    if (!pdfPageInfo) {
      return;
    }

    const width = documentSize?.width || 0;
    const height = documentSize?.height || 0;
    const {width: pageWidth, height: pageHeight} = pdfPageInfo.getViewport({scale: 1})
    // calculate the scale
    const x = width / pageWidth;
    const y = height / pageHeight;

    setPageScale({
      x: isFinite(x) && isNumber(x) ? x : 1,
      y: isFinite(y) && isNumber(y) ? y : 1,
    })
  }, [documentSize?.width, documentSize?.height]);

  function onPdfLoadSuccess(pdfInfo: PDFPageProxy) {
    setPdfPageInfo(pdfInfo);
    addPdfInfo(props.index, pdfInfo);
    props?.onPdfLoadSuccess(props.index);
  }

  function onRenderSuccess() {
    if (props.index === 0 && pageCanvasRef.current) {
      pageCanvasRef.current?.toBlob(blob => {
        if (blob) sendPdfCoverData(blob)
      }, "image/jpeg");
    }
  }

  return (
    <div ref={documentRef} style={{
      position: "relative",
    }}>
      <Page pageNumber={props.index + 1}
            scale={RENDER_SCALE}
            renderTextLayer={false}
            onLoadSuccess={(page) => onPdfLoadSuccess(page)}
            onRenderSuccess={onRenderSuccess}
            canvasRef={pageCanvasRef as any}
      />
      <div className="draw-div"
           style={{
             zIndex: activeLayer === LAYER_TYPE.PEN ? 100 : 10,
             pointerEvents: activeLayer === LAYER_TYPE.PEN ? "auto" : "none",
           }}
      >
        <DrawPen pageIndex={props.index} pageSize={elementSize} pageScale={pageScale}/>
      </div>
      <div className="draw-div"
           style={{
             "zIndex": activeLayer === LAYER_TYPE.RECT ? 100 : 10,
             "pointerEvents": activeLayer === LAYER_TYPE.RECT ? "auto" : "none",
           }}
      >
        <DrawRect documentDivRef={documentRef}
                  documentCanvasRef={pageCanvasRef}
                  pageIndex={props.index}
                  pageSize={elementSize}
                  pageScale={pageScale}
        />
      </div>
      {/*<div className="draw-div"*/}
      {/*     style={{*/}
      {/*       "zIndex": activeLayer === LAYER_TYPE.PICTURE ? 100 : 10,*/}
      {/*       "pointerEvents": activeLayer === LAYER_TYPE.PICTURE ? "auto" : "none",*/}
      {/*     }}*/}
      {/*>*/}
      {/*  <DrawPicture />*/}
      {/*</div>*/}
    </div>
  )
}

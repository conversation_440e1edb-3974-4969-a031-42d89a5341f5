import { PenType, usePenStates } from "@/store/pen-info-store";
import { usePictureStore } from "@/store/picture-info-store";
import { useRectToolboxStore } from "@/store/rect-toolbox-store";
import { LAYER_TYPE } from "@/constant";

export default function useMessageReceiver() {
  const [setPenType, setPenColor, setPenWidth] = usePenStates((state) => [
    state.setPenType,
    state.setPenColor,
    state.setPenWidth,
  ]);
  const [addPicture, setPictureEditMode] = usePictureStore((state) => [
    state.addPicture,
    state.setEditMode,
  ]);
  const [setActiveRect] = useRectToolboxStore((state) => [state.setActiveRect]);

  function setLayerData(messageData: any) {
    const { layerType, layerData } = messageData;
    if (layerType !== LAYER_TYPE.RECT) {
      setActiveRect(null);
    }
    switch (layerType) {
      case LAYER_TYPE.PEN:
        const { type, color, width } = toPenData(layerData);
        setPenType(type);
        setPenColor(color);
        setPenWidth(width);
        break;
      case LAYER_TYPE.PICTURE:
        const { imageFile, inEditMode } = toPictureData(layerData);
        if (imageFile) {
          addPicture({ uid: Date.now(), file: imageFile });
        }
        setPictureEditMode(inEditMode);
        break;
    }
  }

  return { setLayerData };
}

function toPenData(layerData: any) {
  let { type, color, width } = layerData;
  // type to PenType
  switch (type) {
    case "HAND":
      type = PenType.HAND;
      break;
    case "PEN":
      type = PenType.PEN;
      break;
    case "HIGHLIGHT_PEN":
      type = PenType.HIGHLIGHT_PEN;
      break;
    case "HIGHLIGHT_PEN_STRAIGHT":
      type = PenType.HIGHLIGHT_PEN_STRAIGHT;
      break;
    case "LINE":
      type = PenType.LINE;
      break;
    case "DOTTED_LINE":
      type = PenType.DOTTED_LINE;
      break;
    case "ERASER":
      type = PenType.ERASER;
      break;
    default:
      type = PenType.PEN;
  }
  // rgb to rgba
  if (color.startsWith("rgb")) {
    // 荧光笔的alpha值为0.3
    if (
      type === PenType.HIGHLIGHT_PEN ||
      type === PenType.HIGHLIGHT_PEN_STRAIGHT
    ) {
      color = color.replace("rgb", "rgba").replace(")", ", 0.3)");
    } else {
      color = color.replace("rgb", "rgba").replace(")", ", 1)");
    }
  }
  // width to number
  width = Number(width);
  return { type, color, width };
}

function toPictureData(layerData: any) {
  const { imageFile, inEditMode } = layerData;
  return {
    imageFile,
    inEditMode: !!inEditMode,
  };
}

import {APP_ACTION_TYPE, LAYER_TYPE} from "@/constant";
import {usePdfInfoStore} from "@/store/pdf-info-store";

export default function useAppLayerMessage() {
  const [setCurrentFileId] = usePdfInfoStore(state => [state.setCurrentFileId]);
  const [activePageNumber, setActivePageNumber] = usePdfInfoStore(state => [state.activePageNumber, state.setActivePageNumber]);
  const [metaData, setMetaData] = usePdfInfoStore(state => [state.metaData, state.setMetaData]);
  const [setPdfFile, setActiveLayer] = usePdfInfoStore(state => [state.setPdfFile, state.setActiveLayer]);
  const [setAddImgTag] = usePdfInfoStore(state => [state.setAddImgTag]);

  function handleMessageReceive(e: MessageEvent) {
    const { type, file, fileId, fileUid } = e.data;
    if (Array.isArray(file) && Array.isArray(fileUid)) {
      file.forEach((f, index) => {
        f.uid = fileUid[index];
      });
    }
    if (type === APP_ACTION_TYPE.UPLOAD) {
      setCurrentFileId(fileId);
      setActiveLayer(LAYER_TYPE.RECT);
      if (file) {
        if (file.type === "application/pdf") {
          setPdfFile(file);
          setMetaData({...metaData, fileType: "PDF"})
        } else if (file.type === "image/png" || file.type === "image/jpeg" || file.type === "image/jpg") {
          setPdfFile(file);
          setMetaData({...metaData, fileType: "PICTURE"})
        } else if (Array.isArray(file)) {
          setPdfFile(file);
          setMetaData({...metaData, fileType: "PICTURE"});
        }
      }
    } else if (type === APP_ACTION_TYPE.ADD_IMG) {
      if (file && Array.isArray(file)) {
        setPdfFile(file);
        setAddImgTag(Date.now());
      }
    } else if (type === APP_ACTION_TYPE.DEL_PAGE) {
      const data = {
        layerType: LAYER_TYPE.APP,
        type: APP_ACTION_TYPE.DEL_PAGE,
        page: activePageNumber,
      }
      window.parent.postMessage(data, "*");
    }
  }

  return {
    handleMessageReceive,
  }
}

export function sendAddImgLoadedMessageBack() {
  const data = {
    layerType: LAYER_TYPE.APP,
    type: APP_ACTION_TYPE.ADD_IMG_LOADED,
  }
  window.parent.postMessage(data, "*");
}

export function sendPdfCoverData(data: Blob) {
  const dataToSend = {
    layerType: LAYER_TYPE.APP,
    type: APP_ACTION_TYPE.PDF_COVER_DATA,
    data,
  }
  window.parent.postMessage(dataToSend, "*");
}

import React, { useEffect, useMemo, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { Layer, Rect, Stage, Transformer } from "react-konva";
import { KonvaEventObject } from "konva/lib/Node";
import {
  useDebounceFn,
  useEventListener,
  useLongPress,
  usePrevious,
} from "ahooks";
import { IRectAnnotation } from "@/db/app-db";
import {
  deleteRectsByUIDList,
  getRectByUID,
  saveRects,
  useGetPageRects,
} from "@/db/rect-data-hooks";
import {
  ILayerScale,
  ILayerSize,
  usePdfInfoStore,
} from "@/store/pdf-info-store";
import { useRectToolboxStore } from "@/store/rect-toolbox-store";
import { LAYER_TYPE, RENDER_SCALE } from "@/constant";
import FloatToolbox from "@/component/float-toolbox";

type DrawEvent = KonvaEventObject<MouseEvent> | KonvaEventObject<TouchEvent>;

type PressEvent = DrawEvent | TouchEvent | MouseEvent;

interface DrawRectProps {
  documentDivRef: React.RefObject<HTMLDivElement | undefined>;
  documentCanvasRef: React.RefObject<HTMLCanvasElement | undefined>;
  pageIndex: number;
  pageSize: ILayerSize;
  pageScale: ILayerScale;
}

type StagePointerPosition = {
  x: number,
  y: number,
}

export const DrawRect = (props: DrawRectProps) => {
  const { documentDivRef, pageIndex, pageSize, pageScale } = props;
  const [currentFileId] = usePdfInfoStore((state) => [state.currentFileId]);
  const [activePageNumber, setActivePageNumber] = usePdfInfoStore((state) => [
    state.activePageNumber,
    state.setActivePageNumber,
  ]);
  const [metaData] = usePdfInfoStore((state) => [state.metaData]);
  const [activeLayer] = usePdfInfoStore((state) => [state.activeLayer]);
  const annotations = useGetPageRects(currentFileId, pageIndex);
  const [newAnnotation, setNewAnnotation] = useState<IRectAnnotation>();
  const [activeRect] = useRectToolboxStore((state) => [state.activeRect]);
  const previousActiveRect = usePrevious(activeRect);
  const [setActiveRect, setToolboxPosition] = useRectToolboxStore((state) => [
    state.setActiveRect,
    state.setToolboxPosition,
  ]);
  const [scrollRectUid, setScrollRectUid] = useRectToolboxStore((state) => [
    state.scrollRectUid,
    state.setScrollRectUid,
  ]);
  const stageRef = useRef(null);
  const selectedRectRef = useRef<IRectAnnotation>();
  const rectIsLongPressDrawingRef = useRef<boolean>(false);
  const stageClickPositionRef = useRef<StagePointerPosition>({x: 0, y: 0})

  const minimumRectWidth = 10;
  const minimumRectHeight = 10;
  const defaultRectWidth = pageSize.width * 0.1;
  const defaultRectHeight = (defaultRectWidth * 9) / 16;

  const annotationsToDraw = useMemo(() => {
    return newAnnotation ? [...annotations, newAnnotation] : annotations;
  }, [annotations, newAnnotation]);

  // 跳转至指定矩形
  useEffect(() => {
    if (scrollRectUid) {
      const rect = annotations.find((rect) => rect.uid === scrollRectUid);
      if (rect) {
        const divElement = documentDivRef.current;
        if (!divElement) {
          return;
        }
        const divClientRect = divElement.getBoundingClientRect();
        const ry = rect.y * pageScale.y;
        window.scrollTo({
          top:
            divClientRect.top - document.body.getBoundingClientRect().top - ry,
          behavior: "smooth",
        });
        setScrollRectUid(0);
        setActivePageNumber(pageIndex);
      }
    }
  }, [scrollRectUid]);

  // 切换其他图层时，取消激活
  useEffect(() => {
    if (activeLayer !== LAYER_TYPE.RECT || activePageNumber !== pageIndex) {
      setActiveRect(null);
      selectedRectRef.current = undefined;
    }
  }, [activeLayer, activePageNumber]);

  // 删除未添加到笔记的未激活矩形
  useEffect(() => {
    if (
      (!activeRect && !selectedRectRef.current) ||
      (activeRect && activeRect.uid !== previousActiveRect?.uid)
    ) {
      const ids = annotations.filter((r) => !r.display).map((r) => r.uid);
      deleteRectsByUIDList(ids);
    }
  }, [activeRect, selectedRectRef.current]);

  function handleMouseDown(event: DrawEvent) {
    if (newAnnotation) {
      return;
    }

    if (selectedRectRef.current) {
      return;
    }

    const position = event.target?.getStage()?.getPointerPosition();
    if (!position) {
      return;
    }
    // 按比例缩放
    position.x = position.x / pageScale.x;
    position.y = position.y / pageScale.y;
    stageClickPositionRef.current = position;

    setActivePageNumber(pageIndex);
  }

  function handleMouseUp() {
    rectIsLongPressDrawingRef.current = false;
  }

  function handleRightClick(event: DrawEvent, _rect?: IRectAnnotation) {
    if (event.evt.cancelable) {
      event.evt.preventDefault();
    }
    // sendToolbarMessage(event, rect);
    setActivePageNumber(pageIndex);
  }

  function handleRectClick(event: PressEvent, rect?: IRectAnnotation) {
    if (rectIsLongPressDrawingRef.current) {
      return;
    }
    sendToolbarMessage(event, rect);
    setActivePageNumber(pageIndex);
  }

  function handleStageClick(event: PressEvent) {
    if (selectedRectRef.current) {
      selectedRectRef.current = undefined;
    }
    sendToolbarMessage(event);
    setActivePageNumber(pageIndex);
  }

  function handleRectLongPress(_event: PressEvent, rect?: IRectAnnotation) {
    setActiveRect(null);
    handleRectUpdate(rect);
    setActivePageNumber(pageIndex);
  }

  function handleRectUpdate(rect?: IRectAnnotation) {
    if (selectedRectRef.current) {
      getRectByUID(selectedRectRef.current.uid).then((rect) => {
        if (rect) {
          const dataUrl = cropCanvas(props, rect, metaData.fileType);
          saveRects({ ...rect, dataUrl });
        }
      });
    }
    selectedRectRef.current = rect;
  }

  function handleStageLongClick(event: PressEvent) {
    if (selectedRectRef.current) {
      return;
    }

    setNewAnnotation(undefined);
    rectIsLongPressDrawingRef.current = true;

    let evt;
    if (event instanceof MouseEvent) {
      evt = event;
    } else if (event instanceof TouchEvent) {
      evt = event;
    } else {
      evt = event.evt;
    }
    if (evt.cancelable) {
      evt.preventDefault();
    }
    const {x: positionX, y: positionY} = stageClickPositionRef.current;
    const annotationToAdd = {
      fileId: currentFileId,
      uid: Date.now(),
      pageIndex: pageIndex,
      display: false,
      x: Math.max(0, positionX - defaultRectWidth / pageScale.x / 2),
      y: Math.max(0, positionY - defaultRectHeight / pageScale.y / 2),
      width: defaultRectWidth / pageScale.x,
      height: defaultRectHeight / pageScale.y,
      color: "rgb(21, 126, 251)",
    };

    const dataUrl = cropCanvas(
      props,
      { ...annotationToAdd },
      metaData.fileType
    );
    const nRect = { ...annotationToAdd, dataUrl };
    saveRects(nRect);
    selectedRectRef.current = nRect;
    setActivePageNumber(pageIndex);
  }

  useLongPress(
    (event) => {
      handleStageLongClick(event);
    },
    stageRef,
    {
      moveThreshold: { x: minimumRectWidth, y: minimumRectHeight },
      onClick: (event) => handleStageClick(event),
    }
  );

  function sendToolboxMessage(event: PressEvent, rect?: IRectAnnotation) {
    let evt;
    if (event instanceof MouseEvent) {
      evt = event;
    } else if (event instanceof TouchEvent) {
      evt = event;
    } else {
      evt = event.evt;
    }
    if (evt.cancelable) {
      evt.preventDefault();
    }
    // get mouse pointer position
    const { positionX, positionY } = getMousePosition(event);

    setActiveRect(rect ? rect : null);
    handleRectUpdate(undefined);

    // 获取点击相对于视窗的位置
    let clientX = 0;
    let clientY = 0;
    if (evt instanceof MouseEvent) {
      clientX = evt.clientX;
      clientY = evt.clientY;
    } else if (evt instanceof TouchEvent) {
      const touch = evt.changedTouches[0];
      if (touch) {
        clientX = touch.clientX;
        clientY = touch.clientY;
      }
    }
    setToolboxPosition({ x: positionX, y: positionY, clientX, clientY });
  }

  function getMousePosition(event: PressEvent) {
    let evt;
    if (event instanceof MouseEvent) {
      evt = event;
    } else if (event instanceof TouchEvent) {
      evt = event;
    } else {
      evt = event.evt;
    }
    // get mouse pointer position
    let positionX = 0;
    let positionY = 0;
    if (evt instanceof MouseEvent) {
      positionX = evt.offsetX;
      positionY = evt.offsetY;
    } else if (evt instanceof TouchEvent) {
      const touch = evt.changedTouches[0];
      const targetRect = (evt.target as Element).getBoundingClientRect();
      if (touch) {
        positionX = touch.clientX - targetRect.left;
        positionY = touch.clientY - targetRect.top;
      }
    }

    return { positionX, positionY };
  }

  const { run: sendToolbarMessage } = useDebounceFn(sendToolboxMessage, {
    wait: 100,
    leading: true,
    trailing: false,
  });

  useEventListener("mouseup", handleMouseUp, { target: window });

  return (
    <div style={{ width: "100%", height: "100%" }}>
      {createPortal(
        <FloatToolbox
          documentDivRef={documentDivRef}
          pageIndex={pageIndex}
          pageScaleX={pageScale.x}
          pageScaleY={pageScale.y}
        />,
        document.body
      )}
      <Stage
        ref={stageRef}
        width={pageSize.width}
        height={pageSize.height}
        // onClick={handleStageClick}
        onContextMenu={handleRightClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onTouchStart={handleMouseDown}
        onTouchEnd={handleMouseUp}
        scaleX={pageScale.x}
        scaleY={pageScale.y}
      >
        <Layer>
          {annotationsToDraw.map((rect, index) => (
            <RectComponent
              key={index}
              index={index}
              rect={rect}
              isSelected={rect.uid === selectedRectRef.current?.uid}
              handleRightClick={handleRightClick}
              handleRectClick={handleRectClick}
              handleLongPress={handleRectLongPress}
            />
          ))}
        </Layer>
      </Stage>
    </div>
  );
};

type RectComponentProps = {
  index: number;
  rect: IRectAnnotation;
  handleRightClick: (event: DrawEvent, rect?: IRectAnnotation) => void;
  handleRectClick: (event: PressEvent, rect?: IRectAnnotation) => void;
  handleLongPress: (event: PressEvent, rect?: IRectAnnotation) => void;
  isSelected?: boolean;
};

function RectComponent(props: RectComponentProps) {
  const rectRef = useRef<any>();
  const trRef = useRef<any>();

  useEffect(() => {
    if (props.isSelected) {
      trRef.current.nodes([rectRef.current]);
      trRef.current.getLayer().batchDraw();
    }
  }, [props.isSelected]);

  function onDragEnd() {
    const node = rectRef.current;
    saveRects({
      ...props.rect,
      x: node.x(),
      y: node.y(),
    });
  }

  function onTransformEnd() {
    const node = rectRef.current;
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();
    node.scaleX(1);
    node.scaleY(1);

    saveRects({
      ...props.rect,
      x: node.x(),
      y: node.y(),
      width: node.width() * scaleX,
      height: node.height() * scaleY,
    });
  }

  useLongPress(
    (event) => {
      props.handleLongPress(event, props.rect);
    },
    rectRef,
    {
      onClick: (event) => {
        props.handleRectClick(event, props.rect);
      },
    }
  );

  return (
    <>
      <Rect
        ref={rectRef}
        x={props.rect.x}
        y={props.rect.y}
        width={props.rect.width}
        height={props.rect.height}
        fill={colorRgbToRgba(props.rect.color)}
        stroke="transparent"
        // onClick={(event) => props.handleRectClick(event, props.rect)}
        // onTouchStart={(event) => props.handleRectClick(event, props.rect)}
        onContextMenu={(event) => props.handleRightClick(event, props.rect)}
        draggable={props.isSelected}
        onDragEnd={onDragEnd}
        onTransformEnd={onTransformEnd}
      />
      {props.isSelected && (
        <Transformer
          ref={trRef}
          keepRatio={false}
          rotateEnabled={false}
          boundBoxFunc={(oldBox, newBox) => {
            // limit resize
            if (Math.abs(newBox.width) < 10 || Math.abs(newBox.height) < 10) {
              return oldBox;
            }
            return newBox;
          }}
        />
      )}
    </>
  );
}

function cropCanvas(
  props: DrawRectProps,
  rect: IRectAnnotation,
  fileType: "PICTURE" | "PDF"
) {
  const { documentCanvasRef } = props;
  const canvas = documentCanvasRef?.current;
  if (!canvas) {
    return;
  }
  const ctx = canvas.getContext("2d", { willReadFrequently: true });
  if (!ctx) {
    return;
  }
  const pixelRation = fileType === "PDF" ? window.devicePixelRatio || 1 : 1;
  // crop document canvas by rect
  const imageData = ctx.getImageData(
    rect.x * RENDER_SCALE * pixelRation,
    rect.y * RENDER_SCALE * pixelRation,
    rect.width * RENDER_SCALE * pixelRation,
    rect.height * RENDER_SCALE * pixelRation
  );
  const croppedCanvas = document.createElement("canvas");
  croppedCanvas.width = rect.width * RENDER_SCALE * pixelRation;
  croppedCanvas.height = rect.height * RENDER_SCALE * pixelRation;
  const croppedCtx = croppedCanvas.getContext("2d", {
    willReadFrequently: true,
  });
  if (!croppedCtx) {
    return;
  }
  croppedCtx.putImageData(imageData, 0, 0);
  return croppedCanvas.toDataURL("image/jpeg");
}

function colorRgbToRgba(color?: string) {
  if (!color) {
    return "rgba(21, 126, 251, 0.5)";
  }
  if (color.startsWith("rgb") && !color.startsWith("rgba")) {
    color = color.replace("rgb", "rgba").replace(")", ", 0.5)");
  }
  return color;
}

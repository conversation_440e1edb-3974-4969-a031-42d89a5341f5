import { IOcrRect } from "@/db/app-db";
import type { UnifiedBox, UnifiedLine } from "./box-types";

export function groupBoxesByLine(
  boxes: IOcrRect[],
  scaleX: number,
  scaleY: number
) {
  const scaledBoxes = boxes.map((box) => ({
    ...box,
    rect: {
      left: box.rect.left * scaleX,
      right: box.rect.right * scaleX,
      top: box.rect.top * scaleY,
      bottom: box.rect.bottom * scaleY,
    },
  }));

  const groups = [];
  let prevBox = null;
  let currentGroup = [];
  for (const box of scaledBoxes) {
    // 当前的box的x小于前一个box的x时认为换行
    if (prevBox && box.rect.left < prevBox.rect.left) {
      groups.push(currentGroup);
      currentGroup = [];
    }
    currentGroup.push(box);
    prevBox = box;
  }

  // 添加最后一个分组
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  // 计算每个分组的平均top和bottom和高度
  const groupedBoxes = groups.map((group, index) => {
    const top =
      group.reduce((acc, curr) => acc + curr.rect.top, 0) / group.length;
    const bottom =
      group.reduce((acc, curr) => acc + curr.rect.bottom, 0) / group.length;
    const height = bottom - top;
    return {
      group: group.map((box) => ({
        ...box,
        lineTop: top,
        lineBottom: bottom,
        lineHeight: height,
        lineIndex: index,
      })),
      top,
      bottom,
      height,
    };
  });

  //  // 对一个组中top或bottom严重偏离平均值的box进行修正
  //  groupedBoxes.forEach((groupedBox) => {
  //   groupedBox.group.forEach((box) => {
  //     if (box.rect.top < groupedBox.top - groupedBox.height * 0.5) {
  //       box.rect.top = groupedBox.top;
  //     }
  //     if (box.rect.bottom > groupedBox.bottom + groupedBox.height * 0.5) {
  //       box.rect.bottom = groupedBox.bottom;
  //     }
  //   });
  // });

  // 将两个在x轴上有重叠的box分离
  groupedBoxes.forEach((groupedBox) => {
    for (let i = 0; i < groupedBox.group.length - 1; i++) {
      const box1 = groupedBox.group[i];
      const box2 = groupedBox.group[i + 1];

      if (!box1 || !box2) {
        continue;
      }

      if (box1.rect.right > box2.rect.left) {
        box1.rect.right = box2.rect.left;
      }
    }
  });

  // 将groupedBoxes展开成list
  return groupedBoxes.flatMap((groupedBox) => groupedBox.group);
}

export function joinSentences(boxes: UnifiedBox[]) {
  return joinSentencesLocal(boxes);
}

function joinSentencesLocal(boxes: IOcrRect[]) {
  // 当前的box的x小于前一个box的x时认为换行
  const sentences = [];
  let prevBox = null;
  let currentSentence = "";
  for (const box of boxes) {
    if (prevBox && box.rect.left < prevBox.rect.right) {
      sentences.push(currentSentence);
      currentSentence = "";
    }
    currentSentence += box.text + " ";
    prevBox = box;
  }

  sentences.push(currentSentence);

  // 将sentences连接成一个字符串
  const s = sentences.length > 1 ? sentences.join("\n") : sentences[0] ?? "";
  return s.trim();
}

export function joinOriginalSentences(
  allBoxes: UnifiedBox[],
  selectedBoxes: UnifiedBox[]
) {
  const selectedBoxesIds = selectedBoxes.map((box) => box.uid);

  let originalSentencesPrefixBoxes = [];
  let originalSentencesSuffixBoxes = [];

  let prevBox = null;
  let isPrefixEnd = false;
  for (const box of allBoxes) {
    if (prevBox && box.rect.left < prevBox.rect.right) {
      // 大换行清空当前句子
      if (box.lineTop - prevBox.lineBottom > box.lineHeight * 1.5) {
        if (!isPrefixEnd) {
          originalSentencesPrefixBoxes = [];
        } else if (isPrefixEnd && !selectedBoxesIds.includes(box.uid)) {
          break;
        }
      }
    }

    if (selectedBoxesIds.includes(box.uid)) {
      prevBox = box;
      isPrefixEnd = true;
      continue;
    }

    // 选中句尾包含句号、问号或感叹号时，认为是句子的结束
    if (
      isPrefixEnd &&
      (prevBox?.text.includes(".") ||
        prevBox?.text.includes("?") ||
        prevBox?.text.includes("!"))
    ) {
      break;
    }

    if (
      box.text.includes(".") ||
      box.text.includes("?") ||
      box.text.includes("!")
    ) {
      if (!isPrefixEnd) {
        originalSentencesPrefixBoxes = [];
        prevBox = box;
        continue;
      } else {
        originalSentencesSuffixBoxes.push(box);
        break;
      }
    }

    if (!isPrefixEnd) {
      originalSentencesPrefixBoxes.push(box);
    } else {
      originalSentencesSuffixBoxes.push(box);
    }

    prevBox = box;
  }

  return [
    originalSentencesPrefixBoxes.map((box) => box.text).join(" "),
    originalSentencesSuffixBoxes.map((box) => box.text).join(" "),
  ];
}

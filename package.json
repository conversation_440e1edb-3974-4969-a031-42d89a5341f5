{"name": "react-pdf-sample-page", "version": "4.0.0", "description": "A sample page for React-PDF.", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite --host", "preview": "vite preview", "pack": "npm run build && bash scripts/pack.sh"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@tanstack/react-virtual": "^3.13.12", "@zip.js/zip.js": "^2.7.63", "ahooks": "^3.9.0", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "immutable": "^5.1.3", "jspdf": "^3.0.1", "konva": "^9.3.20", "lodash": "^4.17.21", "pinch-zoom-js": "^2.3.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-konva": "^18.2.10", "react-pdf": "^9.2.1", "use-image": "^1.1.4", "zustand": "^4.5.5"}, "devDependencies": {"@heroicons/react": "^2.2.0", "@types/lodash": "^4.17.20", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "daisyui": "^4.12.24", "postcss": "^8.5.6", "react-daisyui": "^5.0.5", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5"}}
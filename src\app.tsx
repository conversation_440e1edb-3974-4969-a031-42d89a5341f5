import React, { useEffect, useMemo, useRef, useState } from "react";
import { Document, pdfjs } from "react-pdf";
import { PDFDocumentProxy } from "pdfjs-dist";
import { useDebounceFn, useEventListener } from "ahooks";
import PinchZoom from "@/lib/pinch-zoom/pinch-zoom";

import { APP_ACTION_TYPE, LAYER_TYPE } from "@/constant";
import { usePdfInfoStore } from "@/store/pdf-info-store";
import { useRectToolboxStore } from "@/store/rect-toolbox-store";
import { usePinchZoomStore } from "@/store/pinch-zoom-store";
import useMessageReceiver from "@/lib/message-receiver";
import useRectLayerMessageReceiver from "@/lib/rect-layer-message";
import useAppLayerMessage, {
  sendAddImgLoadedMessageBack,
} from "@/lib/app-layer-message";
import PageContainer from "@/component/page-container";
import ImageContainer from "@/component/image-container/index";
import MultiImagesContainer from "@/component/multi-images-container";

import "react-pdf/dist/Page/AnnotationLayer.css";
import "@/app.css";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.js",
  import.meta.url
).toString();

const options = {
  cMapUrl: "cmaps/",
  standardFontDataUrl: "standard_fonts/",
};

export default function App() {
  const pinchContainerRef = useRef<HTMLDivElement>(null);
  const [initCount, setInitCount] = useState<number>(0);
  const [imgLoadedCount, setImgLoadedCount] = useState<number>(0);
  const [numPages, setNumPages] = useState<number>();
  const [activeLayer, setActiveLayer] = usePdfInfoStore((state) => [
    state.activeLayer,
    state.setActiveLayer,
  ]);
  const [file] = usePdfInfoStore((state) => [state.pdfFile]);
  const [addImgTag] = usePdfInfoStore((state) => [state.addImgTag]);
  const [setZoomScale] = useRectToolboxStore((state) => [state.setZoomScale]);
  const [
    pinchZoomInstance,
    setPinchZoomInstance,
    enableDraggable,
    disableDraggable,
  ] = usePinchZoomStore((state) => [
    state.pinchZoomInstance,
    state.setPinchZoomInstance,
    state.enableDraggable,
    state.disableDraggable,
  ]);
  const { setLayerData } = useMessageReceiver();
  const { handleMessageReceive: handleAppMessageReceive } =
    useAppLayerMessage();

  useEventListener(
    "message",
    (e: MessageEvent) => {
      const { layerType } = e.data;
      if (
        layerType === LAYER_TYPE.PEN ||
        layerType === LAYER_TYPE.RECT ||
        layerType === LAYER_TYPE.PICTURE
      ) {
        setActiveLayer(layerType);
        setLayerData(e.data);
      } else {
        setActiveLayer("");
      }

      if (layerType === LAYER_TYPE.APP) {
        handleAppMessageReceive(e);
      }
    },
    {}
  );

  // 注册框选事件
  useRectLayerMessageReceiver();

  useEffect(() => {
    const data = {
      layerType: LAYER_TYPE.APP,
      type: APP_ACTION_TYPE.INIT,
    };
    window.parent.postMessage(data, "*");
  }, []);

  useEffect(() => {
    setImgLoadedCount(0);
  }, [addImgTag]);

  useEffect(() => {
    if (
      addImgTag > 0 &&
      Array.isArray(file) &&
      imgLoadedCount === file.length
    ) {
      sendAddImgLoadedMessageBack();
    }
  }, [imgLoadedCount]);

  useEffect(() => {
    if (!pinchContainerRef.current || pinchZoomInstance) {
      return;
    }

    if (
      file instanceof File &&
      (file.type === "image/png" ||
        file.type === "image/jpeg" ||
        file.type === "image/jpg") &&
      initCount === 1
    ) {
      setPinchZoomInstance(
        new PinchZoom(pinchContainerRef.current, {
          use2d: false,
          setOffsetsOnce: true,
          draggableUnzoomed: false,
          onZoomUpdate: onZoomUpdate,
          tapZoomFactor: 1,
        })
      );
    } else if (Array.isArray(file) && initCount === file.length) {
      setPinchZoomInstance(
        new PinchZoom(pinchContainerRef.current, {
          use2d: false,
          setOffsetsOnce: true,
          draggableUnzoomed: false,
          onZoomUpdate: onZoomUpdate,
          tapZoomFactor: 1,
        })
      );
    } else if (initCount === numPages && initCount !== 0) {
      setPinchZoomInstance(
        new PinchZoom(pinchContainerRef.current, {
          use2d: false,
          setOffsetsOnce: true,
          draggableUnzoomed: false,
          onZoomUpdate: onZoomUpdate,
          tapZoomFactor: 1,
        })
      );
    }
  }, [initCount, file, numPages]);

  const { run: onZoomUpdate } = useDebounceFn(
    (target: PinchZoom) => {
      // @ts-ignore
      setZoomScale(target.zoomFactor || 1);
    },
    { wait: 50, leading: false, trailing: true }
  );

  useEffect(() => {
    if (activeLayer === LAYER_TYPE.PEN || activeLayer === LAYER_TYPE.PICTURE) {
      disableDraggable();
    } else {
      enableDraggable();
    }
  }, [activeLayer]);

  function onDocumentLoadSuccess({ numPages: nextNumPages }: PDFDocumentProxy) {
    setNumPages(nextNumPages);
  }

  function onImageLoadSuccess() {
    setInitCount((prev) => prev + 1);
    setImgLoadedCount((prev) => prev + 1);
  }

  function onPdfLoadSuccess(index: number) {
    setInitCount(Math.max(initCount, index + 1));
  }

  if (!file) {
    return <h1>文档载入中...</h1>;
  }

  if (
    file instanceof File &&
    (file.type === "image/png" ||
      file.type === "image/jpeg" ||
      file.type === "image/jpg")
  ) {
    return (
      <div ref={pinchContainerRef} style={{ minHeight: "100vh" }}>
        <div className="Example__container__document">
          <ImageContainer
            file={file}
            index={0}
            totalPage={1}
            onLoadSuccess={onImageLoadSuccess}
          />
        </div>
      </div>
    );
  }

  if (Array.isArray(file)) {
    return (
      <div ref={pinchContainerRef} style={{ minHeight: "100vh" }}>
        <div className="Example__container__document">
          <MultiImagesContainer
            files={file}
            onLoadSuccess={onImageLoadSuccess}
          />
        </div>
      </div>
    );
  }

  return (
    <div ref={pinchContainerRef} style={{ minHeight: "100vh" }}>
      <div className="Example__container__document">
        <Document
          file={file}
          onLoadSuccess={onDocumentLoadSuccess}
          options={options}
        >
          {Array.from(new Array(numPages), (_, index) => (
            <PageContainer
              key={index}
              index={index}
              onPdfLoadSuccess={onPdfLoadSuccess}
            />
          ))}
        </Document>
      </div>
    </div>
  );
}

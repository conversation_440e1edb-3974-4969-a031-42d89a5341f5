import React, {useEffect, useRef} from "react";
import {Layer, Stage, Image, Transformer} from "react-konva";
import {KonvaEventObject} from "konva/lib/Node";
import {usePdfInfoStore} from "@/store/pdf-info-store";
import {ICanvasPicture, usePictureStore} from "@/store/picture-info-store";

export const DrawPicture = () => {
  const [pageSize, scale] = usePdfInfoStore(state => [state.pageSize, state.pageScale()]);
  const [images] = usePictureStore(state => [state.pictures]);

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Stage
        width={pageSize.width}
        height={pageSize.height}
        scaleX={scale.x}
        scaleY={scale.y}
      >
        <Layer>
          {images.map((image) => (
            <ImageContainer key={image.uid} image={image} />
          ))}
        </Layer>
      </Stage>
    </div>
  );
}

interface ImageContainerProps {
  image: ICanvasPicture;
}

function ImageContainer(props: ImageContainerProps) {
  const { image } = props;
  const [updateImage, deletePicture, inEditMode] = usePictureStore(state => [state.updatePicture, state.deletePicture, state.inEditMode]);
  const shapeRef = useRef<any>();
  const trRef = useRef<any>();

  if (!image.y) {
    image.x = 0;
    image.y = document.documentElement.scrollTop;
  }

  const fileUrl = URL.createObjectURL(image.file);
  const imageObj = new window.Image();
  imageObj.src = fileUrl;
  imageObj.onload = () => {
    URL.revokeObjectURL(fileUrl)
  }

  useEffect(() => {
    if (!trRef.current) {
      return;
    }

    if (inEditMode) {
      trRef.current.nodes([shapeRef.current]);
    } else {
      trRef.current.nodes([]);
    }
    trRef.current.getLayer().batchDraw();
  }, [shapeRef.current, inEditMode]);

  function onDragEnd() {
    const node = shapeRef.current;
    updateImage({
      ...image,
      x: node.x(),
      y: node.y(),
    });
  }

  function onTransformEnd() {
    const node = shapeRef.current;
    updateImage({
      ...image,
      x: node.x(),
      y: node.y(),
      width: node.width() * node.scaleX(),
      height: node.height() * node.scaleY(),
      rotation: node.rotation(),
    });
  }

  function deleteImage(e: KonvaEventObject<PointerEvent>) {
    e.evt.preventDefault();
    deletePicture(image.uid);
  }

  return (
    <>
      <Image image={imageObj}
             ref={shapeRef}
             x={image.x}
             y={image.y}
             onDragEnd={onDragEnd}
             draggable={inEditMode}
             onTransformEnd={onTransformEnd}
             onContextMenu={deleteImage}
      />
      <Transformer ref={trRef}/>
    </>
  )
}

import <PERSON>ie from "dexie";
import { PenType } from "@/store/pen-info-store";

export class AppDatabase extends <PERSON>ie {
  penData!: Dexie.Table<IPenLine, number>;
  rectData!: Dexie.Table<IRectAnnotation, number>;
  noteData!: Dexie.Table<INoteData, number>;
  ocrData!: Dexie.Table<IOcrData, number>;
  configData!: Dexie.Table<IConfig, ConfigKey>;

  constructor() {
    super("AppDatabase");

    this.version(2).stores({
      colorTagData: "&id",
      penData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
      rectData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
      noteData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
      ocrData: "&uid",
      configData: "&key",
    });
  }
}

const globalForAppDb = globalThis as unknown as {
  appDb: AppDatabase | undefined;
};

const createAppDbInstance = () => {
  const db = new AppDatabase();
  if (!globalForAppDb.appDb) {
    globalForAppDb.appDb = db;
  }

  return db;
};

export const appDbInstance = globalForAppDb.appDb ?? createAppDbInstance();

export enum ConfigKey {
  IsEditorFirstLaunch = "isEditorFirstLaunch",
  ShowResultRect = "showResultRect",
  OCREngineType = "ocrEngineType",
}

export interface IConfig {
  key: ConfigKey;
  value: string | number | boolean;
}

export interface IColorTag {
  id: number;
  color: string;
  name: string;
}

export interface IPenLine {
  fileId: number;
  uid: number;
  pageIndex: number;
  type: PenType;
  color: string;
  width: number;
  points: number[];
  finished?: boolean;
}

export interface IRectAnnotation {
  fileId: number;
  uid: number;
  pageIndex: number;
  display: boolean;
  x: number;
  y: number;
  width: number;
  height: number;
  parentUid?: number;
  color?: string;
  dataUrl?: string;
  wordBoxes?: number[];
}

export interface INoteData {
  fileId: number;
  uid: number;
  noteTitle: string;
  ocrContent: string;
  ocrContentSpan: string;
  keyword: string;
  color: string;
  colorTagUid?: number;
  noteTags: any[];
  noteContentArray: any[];
}

export interface IOcrData {
  uid: number;
  width: number;
  height: number;
  data?: IOcrRectRaw[];
}

export interface IOcrRectRaw {
  uid: number;
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    x1: number;
    y0: number;
    y1: number;
  };
  line?: IOcrLineRaw;
}

export interface IOcrLineRaw {
  uid: number;
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    x1: number;
    y0: number;
    y1: number;
  };
  rowAttributes: {
    row_height: number;
  };
}

export interface IOcrRect {
  uid: number;
  text: string;
  confidence: number;
  rect: {
    left: number;
    right: number;
    top: number;
    bottom: number;
  };
  line?: IOcrLine;
}

export interface IOcrLine {
  text: string;
  confidence: number;
  rect: {
    left: number;
    right: number;
    top: number;
    bottom: number;
  };
  rowAttributes: {
    rowHeight: number;
  };
}

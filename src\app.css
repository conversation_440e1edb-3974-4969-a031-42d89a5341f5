@tailwind base;
@tailwind components;
@tailwind utilities;

/* 隐藏scrollbar */
body::-webkit-scrollbar {
    display: none;
}

body {
    /*overflow: hidden;*/
    -ms-overflow-style: none;
    scrollbar-width: none;
    /* fix ios select issue  */
    -webkit-user-select: none;
}

/* force enable hardware acceleration */
canvas {
    transform: translate3d(0, 0, 0);
}

.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

.Example__container__document {
    background: lightgray;
    overflow: hidden;
}

.Example__container__document .react-pdf__Page {
    margin: 5px 0;
}

.Example__container__document .react-pdf__Page:first-child {
    margin-top: 0;
}

.Example__container__document .react-pdf__Page:last-child {
    margin-bottom: 0;
}

.Example__container__document .react-pdf__Page canvas {
    max-width: 100%;
    height: auto !important;
}

.draw-div {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.draw-div canvas {
    max-width: 100%;
    height: auto !important;
}

.image-container {
    position: relative;
    background-color: white;
}

.image-container {
    margin-bottom: 8px;
 }

.image-container .page-number {
    position: absolute;
    left: 0;
    bottom: 5px;
    height: 1em;
    width: 100%;
    text-align: center;
}

.image-container canvas {
    max-width: 100%;
    height: auto !important;
}

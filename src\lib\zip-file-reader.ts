import * as zip from "@zip.js/zip.js"
import { IMetadata } from "@/store/pdf-info-store";

interface IArchive {
  metadata?: IMetadata;
  pdfFile?: Blob;
  penData?: Array<any>;
  rectData?: Array<any>;
  noteData?: Array<any>;
}

export const readZipFile =  async (file: File): Promise<IArchive> => {
  // read zip file
  const reader = new zip.ZipReader(new zip.BlobReader(file))
  const entries = await reader.getEntries()
  // 数据文件
  const metadataFile = entries.find((entry) => entry.filename === "metadata.json")
  const meta = JSON.parse(await metadataFile?.getData!(new zip.TextWriter("application/json")) || "{}")
  // 内容文件
  const pdfFile = entries.find((entry) => entry.filename.endsWith(".pdf"))
  const pngFile = entries.find((entry) => entry.filename.endsWith(".png"))
  const jpgFile = entries.find((entry) => entry.filename.endsWith(".jpg"))
  let contentFile;
  if (pdfFile) {
    contentFile = await pdfFile.getData!(new zip.BlobWriter("application/pdf"));
  } else if (pngFile) {
    contentFile = await pngFile.getData!(new zip.BlobWriter("image/png"));
  } else if (jpgFile) {
    contentFile = await jpgFile.getData!(new zip.BlobWriter("image/jpeg"));
  }
  if (!contentFile) {
    throw new Error("No content file found in zip archive")
  }
  contentFile = new File([contentFile], getFilename(meta.pdfFileName, Date.now().toString()), { type: contentFile.type })
  // 笔记文件
  const penDataFile = entries.find((entry) => entry.filename === "pen-data.json")
  const rectDataFile = entries.find((entry) => entry.filename === "rect-data.json")
  const noteDataFile = entries.find((entry) => entry.filename === "note-data.json")
  return {
    metadata: meta,
    pdfFile: contentFile,
    penData: JSON.parse(await penDataFile?.getData!(new zip.TextWriter("application/json")) || "[]"),
    rectData: JSON.parse(await rectDataFile?.getData!(new zip.TextWriter("application/json")) || "[]"),
    noteData: JSON.parse(await noteDataFile?.getData!(new zip.TextWriter("application/json")) || "[]"),
  }
}

export const saveZipFile = async (meta: IMetadata, file: File, penLines: any, rects: any, notes: any) => {
  const writer = new zip.ZipWriter(new zip.BlobWriter("application/zip"))
  const metadata = {
    pdfFileName: getFilename(meta.pdfFileName, file.name),
  }
  await writer.add("metadata.json", new zip.TextReader(JSON.stringify(metadata)))
  if (file.type === "application/pdf") {
    await writer.add(`${metadata.pdfFileName}.pdf`, new zip.BlobReader(file))
  } else if (file.type === "image/png") {
    await writer.add(`${metadata.pdfFileName}.png`, new zip.BlobReader(file))
  } else if (file.type === "image/jpeg" || file.type === "image/jpg") {
    await writer.add(`${metadata.pdfFileName}.jpg`, new zip.BlobReader(file))
  }
  await writer.add("pen-data.json", new zip.TextReader(JSON.stringify(penLines)))
  await writer.add("rect-data.json", new zip.TextReader(JSON.stringify(rects)))
  await writer.add("note-data.json", new zip.TextReader(JSON.stringify(notes)))
  const blob = await writer.close()
  return {
    filename: metadata.pdfFileName,
    blob,
  };
}

function getFilename(metaFilename: string, filename: string) {
  if (metaFilename) {
    return metaFilename;
  } else if (filename) {
    // remove file extension
    return filename.replace(/\.[^/.]+$/, "")
  } else {
    // generate filename base on current time, for example: 2021-01-01-12-00-00
    return new Date().toISOString().replace(/[-:T]/g, "").slice(0, -5);
  }
}

export function colorRgbToRgba(color?: string) {
  if (!color) {
    return "rgba(21, 126, 251, 0.5)";
  }
  if (color.startsWith("rgb") && !color.startsWith("rgba")) {
    color = color.replace("rgb", "rgba").replace(")", ", 0.5)");
  }
  return color;
}

export type CanvasPosition = {
  x: number;
  y: number;
};

type OcrDrawRect = {
  x: number;
  y: number;
  width: number;
  height: number;
  fill: string;
  stroke?: string;
  strokeWidth?: number;
};

export function isPointInCircle(
  point: CanvasPosition,
  circle: {
    x: number;
    y: number;
    radius: number;
  }
): boolean {
  const dx = point.x - circle.x;
  const dy = point.y - circle.y;
  return dx * dx + dy * dy <= circle.radius * circle.radius;
}

export function isPointInRect(
  point: CanvasPosition,
  rect: {
    x: number;
    y: number;
    width: number;
    height: number;
  }
): boolean {
  return (
    point.x >= rect.x &&
    point.x <= rect.x + rect.width &&
    point.y >= rect.y &&
    point.y <= rect.y + rect.height
  );
}

export function drawRect(ctx: CanvasRenderingContext2D, rect: OcrDrawRect) {
  ctx.fillStyle = rect.fill || "transparent";
  ctx.strokeStyle = rect.stroke || "transparent";
  ctx.lineWidth = rect.strokeWidth || 1;

  ctx.beginPath();
  ctx.rect(rect.x, rect.y, rect.width, rect.height);
  ctx.fill();
  if (rect.stroke !== "transparent") {
    ctx.stroke();
  }
}

export function drawCircle(
  ctx: CanvasRenderingContext2D,
  circle: {
    x: number;
    y: number;
    radius: number;
    fill?: string;
    stroke?: string;
  }
) {
  ctx.lineWidth = 2;
  ctx.fillStyle = circle.fill || "transparent";
  ctx.strokeStyle = circle.stroke || "transparent";

  ctx.beginPath();
  ctx.arc(circle.x, circle.y, circle.radius, 0, Math.PI * 2);
  if (circle.fill !== "transparent") {
    ctx.fill();
  }
  if (circle.stroke !== "transparent") {
    ctx.stroke();
  }
}

export function drawText(
  ctx: CanvasRenderingContext2D,
  text: {
    x: number;
    y: number;
    text: string;
    fontSize: number;
    fill: string;
  }
) {
  ctx.fillStyle = text.fill;
  ctx.font = `${text.fontSize}px Arial`;
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";
  ctx.fillText(text.text, text.x, text.y);
}

export function drawLine(
  ctx: CanvasRenderingContext2D,
  line: {
    points: number[];
    fill: string;
    stroke: string;
    width: number;
  }
) {
  ctx.strokeStyle = line.stroke;
  ctx.fillStyle = line.fill;
  ctx.lineWidth = line.width;

  ctx.beginPath();
  ctx.moveTo(line.points[0]!, line.points[1]!);
  for (let i = 2; i < line.points.length; i += 2) {
    ctx.lineTo(line.points[i]!, line.points[i + 1]!);
  }
  ctx.stroke();
}

import React, { useEffect, useMemo, useRef, useState, Suspense } from "react";
import {
  ILayerScale,
  ILayerSize,
  usePdfInfoStore,
} from "@/store/pdf-info-store";
import { useAppInfoStore } from "@/store/app-info-store";
import { useSize } from "ahooks";
import { isFinite, isNumber } from "lodash";
import { ErrorBoundary, FallbackProps } from "react-error-boundary";
import { LAYER_TYPE } from "@/constant";
import { DrawPen } from "@/layers/draw-pen";
import { CanvasSelect } from "@/layers/canvas-select";
import ImageContainerCanvas, {
  ImageSize,
} from "@/component/image-container/canvas";

type ImageContainerProps = {
  totalPage: number;
  index: number;
  file: File;
  onLoadSuccess: () => void;
  instantRender?: boolean;
};

export default function ImageContainer(props: ImageContainerProps) {
  const [activeLayer] = usePdfInfoStore((state) => [state.activeLayer]);
  const [activePageNumber, setActivePageNumber] = usePdfInfoStore((state) => [
    state.activePageNumber,
    state.setActivePageNumber,
  ]);
  const [isRendering, setIsRendering] = useAppInfoStore((state) => [
    state.isRendering,
    state.setIsRendering,
  ]);
  const documentRef = useRef<HTMLDivElement>(null);
  const documentSize = useSize(documentRef);
  const [elementSize, setElementSize] = useState<ILayerSize>({
    width: 0,
    height: 0,
  });
  const [pageScale, setPageScale] = useState<ILayerScale>({ x: 1, y: 1 });
  const [imageSize, setImageSize] = useState<ImageSize>({
    width: 0,
    height: 0,
  });
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [renderCanvas, setRenderCanvas] = useState(false);

  // 缩放控制
  useEffect(() => {
    setElementSize({
      width: documentSize?.width || 0,
      height: documentSize?.height || 0,
    });

    const width = documentSize?.width || 0;
    const height = documentSize?.height || 0;
    const { width: imageWidth, height: imageHeight } = imageSize;
    // calculate the scale
    const x = width / imageWidth;
    const y = height / imageHeight;

    setPageScale({
      x: isFinite(x) && isNumber(x) ? x : 1,
      y: isFinite(y) && isNumber(y) ? y : 1,
    });
  }, [documentSize?.width, documentSize?.height]);

  function onLoadSuccess(imageSize: ImageSize) {
    setImageSize(imageSize);
    setIsImageLoaded(true);
    props?.onLoadSuccess();
    if (props.instantRender) {
      handleInstantRender();
    }
  }

  function handleInstantRender() {
    setRenderCanvas(true);
  }

  function handleRenderCanvas() {
    if (!isRendering) {
      setIsRendering(true);
      setRenderCanvas(true);
    }
  }

  function onRenderFinished() {
    setIsRendering(false);
  }

  function setActivePage() {
    setActivePageNumber(props.index);
  }

  const activeBorder = useMemo(() => {
    if (activePageNumber === props.index) {
      return {
        border: "3px solid steelblue",
      };
    } else {
      return {
        border: "3px solid transparent",
      };
    }
  }, [activePageNumber, props.index]);

  return (
    <div
      ref={documentRef}
      className={"image-container"}
      style={activeBorder}
      onClick={setActivePage}
    >
      <ImageContainerCanvas
        imageFile={props.file}
        onLoadSuccess={onLoadSuccess}
      />

      {/* Show render button when image is loaded but canvas is not rendered */}
      {isImageLoaded && !renderCanvas && !props.instantRender && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-base-100/80 z-50">
          <button
            className="btn btn-primary"
            onClick={handleRenderCanvas}
            disabled={isRendering}
          >
            {isRendering ? "渲染中..." : "渲染图层"}
          </button>
        </div>
      )}

      {/* Only render the canvas when both image is loaded and renderCanvas is true */}
      {isImageLoaded && renderCanvas && (
        <ErrorBoundary
          fallbackRender={({ resetErrorBoundary }: FallbackProps) => (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-base-100/80 z-50">
              <button
                className="btn btn-error"
                onClick={() => {
                  onRenderFinished();
                  resetErrorBoundary();
                }}
              >
                渲染出错 重新渲染
              </button>
            </div>
          )}
          onReset={onRenderFinished}
        >
          <Suspense
            fallback={
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-base-100/80 z-50">
                <span className="loading loading-spinner loading-lg text-primary mb-2"></span>
              </div>
            }
          >
            <div
              className="draw-div"
              style={{
                zIndex: activeLayer === LAYER_TYPE.PEN ? 100 : 10,
                pointerEvents: activeLayer === LAYER_TYPE.PEN ? "auto" : "none",
              }}
            >
              <DrawPen
                pageIndex={props.index}
                pageSize={elementSize}
                pageScale={pageScale}
              />
            </div>
            <div
              className="draw-div"
              style={{
                zIndex: activeLayer === LAYER_TYPE.RECT ? 100 : 10,
                pointerEvents:
                  activeLayer === LAYER_TYPE.RECT ? "auto" : "none",
              }}
            >
              <CanvasSelect
                file={props.file}
                pageIndex={props.index}
                pageSize={elementSize}
                imageSize={imageSize}
                pageScale={pageScale}
                onLoadingFinished={onRenderFinished}
              />
            </div>
          </Suspense>
        </ErrorBoundary>
      )}
      <div className="page-number">
        <strong>
          <small>{`${props.index + 1}/${props.totalPage}`}</small>
        </strong>
      </div>
    </div>
  );
}

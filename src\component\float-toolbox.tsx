import React, {useMemo} from 'react';
import { But<PERSON> } from "react-daisyui";
import { BookmarkIcon, SwatchIcon } from "@heroicons/react/24/solid";
import {IRectAnnotation} from "@/db/app-db";
import {deleteRectByUID, getRectByUID, saveRects} from "@/db/rect-data-hooks";
import { useRectToolboxStore } from "@/store/rect-toolbox-store";
import {LAYER_TYPE, RECT_ACTION_TYPE} from "@/constant";

type RectFloatToolboxProps = {
  documentDivRef: React.RefObject<HTMLDivElement | undefined>;
  pageIndex: number
  pageScaleX: number,
  pageScaleY: number,
};

const PresetColors = [
  "rgb(254, 208, 48)",
  "rgb(21, 126, 251)",
  "rgb(252, 49, 66)",
  "rgb(83, 215, 105)",
  "rgb(155, 155, 155)",
]
const DefaultColor = "rgb(21, 126, 251)";

const RectFloatToolbox: React.FC<RectFloatToolboxProps> = (props) => {
  const { documentDivRef, pageIndex} = props;
  const [activeRect, toolboxPosition] = useRectToolboxStore(state => [state.activeRect, state.toolboxPosition]);
  const [zoomScale] = useRectToolboxStore(state => [state.zoomScale])
  const [setActiveRect] = useRectToolboxStore(state => [state.setActiveRect]);

  const showToolbox = useMemo(() => {
    return activeRect?.pageIndex === pageIndex;
  }, [pageIndex, activeRect?.pageIndex]);

  const activeColor = useMemo(() => {
    return activeRect?.color || DefaultColor;
  }, [activeRect?.color]);

  const cardPositionStyle = useMemo(() => {
    const cardWidth: number = 374;
    const cardHeight: number = 116;

    const divClientRect = documentDivRef.current?.getBoundingClientRect();
    const divMaxWidth = (divClientRect?.width ?? 0) - cardWidth;
    const divMaxHeight = (divClientRect?.height ?? 0) - cardHeight;

    const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    const screenMaxWidth = screenWidth - cardWidth;
    const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
    const screenMaxHeight = screenHeight - cardHeight;

    // limit the toolbox position, so that it won't be out of the page
    const position = {
      x: ((activeRect?.x || 0)) * props.pageScaleX,
      y: ((activeRect?.y || 0) + (activeRect?.height || 0)) * props.pageScaleY,
    }

    if (zoomScale !== 1) {
      position.x = toolboxPosition.clientX;

      if (position.x > screenMaxWidth - 32) {
        position.x = screenMaxWidth - 32
      }
    } else {
      if (position.x > divMaxWidth) {
        position.x = divMaxWidth
      }
    }

    if (position.y > divMaxHeight && position.y > cardHeight * 2) {
      position.y = (activeRect?.y || 0) * props.pageScaleY - cardHeight
    } else if (position.y > screenMaxHeight) {
      position.y = (activeRect?.y || 0) * props.pageScaleY - cardHeight
    }

    position.y = position.y * zoomScale
    position.y = position.y + ((divClientRect?.top ?? 0) + window.scrollY)

    return {
      top: Math.max(0, position.y),
      left: position.x,
      zIndex: 200,
      transform: "translate(0, 0)",
    };
  }, [toolboxPosition.x, toolboxPosition.y, documentDivRef.current, toolboxPosition.clientY]);

  function addToNoteCard() {
    const rectUid = activeRect?.uid;
    getRectByUID(rectUid)
      .then(rect => {
        if (rect) {
          saveRects({...rect, display: true});
        }
      })
    setActiveRect(null);
  }

  function addToResearch() {
    const rectUid = activeRect?.uid;
    getRectByUID(rectUid)
      .then(rect => {
        if (rect) {
          sendRectsMessage(RECT_ACTION_TYPE.RESEARCH, rect);
        }
      })
    setActiveRect(null);
  }

  function deleteRectAction() {
    if (!activeRect) return;
    deleteRectByUID(activeRect.uid);
    setActiveRect(null);
  }

  function changeColor(color: string) {
    const rectUid = activeRect?.uid;
    getRectByUID(rectUid)
    .then(rect => {
      if (rect) {
        saveRects({...rect, color: color});
        setActiveRect({...rect, color: color});
      }
    })
  }

  function sendRectsMessage(type: RECT_ACTION_TYPE, nRect?: IRectAnnotation) {
    const data = {
      layerType: LAYER_TYPE.RECT,
      type: type,
      uid: nRect?.uid,
      rect: nRect,
    }
    window.parent.postMessage(data, "*");
  }

  function activeButtonStyle(color: string) {
    return color === activeColor ? { color: color } : {};
  }

  return (
    showToolbox && (
      <div
        className="absolute p-0"
        style={cardPositionStyle}
      >
        <div className="card card-compact bg-white shadow-xl">
          <div className="card-body">
            <div className="flex justify-center items-center bg-white gap-2">
              <Button variant="outline" size="sm">
                复制
              </Button>
              <Button variant="outline" size="sm" onClick={addToNoteCard}>
                加入学习卡
              </Button>
              <Button variant="outline" size="sm">
                朗读
              </Button>
              <Button variant="outline" size="sm" onClick={addToResearch}>
                研究
              </Button>
              <Button variant="outline" color="error" size="sm" onClick={deleteRectAction}>
                删除
              </Button>
            </div>
            <hr className="border-none h-1 bg-gray-300 mx-5"/>
            <div className="flex justify-center items-center bg-white gap-2">
              <Button variant="outline" size="sm">
                <BookmarkIcon className="h-4 w-4 text-gray-700" />
              </Button>
              <Button variant="outline" size="sm">
                <SwatchIcon className="h-4 w-4 text-gray-700" />
              </Button>
              {PresetColors.map((color) => (
                <Button key={color} variant="outline" size="sm"
                        style={activeButtonStyle(color)}
                        onClick={() => changeColor(color)}
                >
                  <div className="w-4 h-4 rounded-full" style={{backgroundColor: color}}/>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  );
}

export default RectFloatToolbox;

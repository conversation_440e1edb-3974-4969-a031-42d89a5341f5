import { appDbInstance, INoteData } from "@/db/app-db";

const db = appDbInstance;

export async function saveNotes(notes: INoteData[] | INoteData) {
  if (Array.isArray(notes)) {
    return db.noteData.bulkPut(notes);
  } else {
    return db.noteData.put(notes);
  }
}

export async function createNoteData(
  fileId: number,
  uid: number,
  ocrString: string,
  prefixSentence: string,
  suffixSentence: string,
  color: string
) {
  const note: INoteData = {
    fileId,
    uid,
    noteTitle: ocrString,
    ocrContent: `${prefixSentence} ${ocrString} ${suffixSentence}`.trim(),
    ocrContentSpan:
      `${prefixSentence} <span style="color: ${color}">${ocrString}</span> ${suffixSentence}`.trim(),
    keyword: "",
    color,
    noteTags: [],
    noteContentArray: [
      // { uid: Date.now(), type: "TEXT", value: ocrString, isContent: true },
    ],
  };

  await saveNotes(note);
}

export async function deleteNoteByUid(uid: number) {
  return db.noteData.delete(uid);
}

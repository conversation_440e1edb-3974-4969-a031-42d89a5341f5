// convert image file to pdf file
import { jsPDF } from "jspdf";

export function imageToPdf(file: File) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = function () {
      const width = img.width;
      const height = img.height;
      const pdf = new jsPDF(width > height ? "l" : "p", "px", [width, height]);
      if (file.type === "image/png") {
        pdf.addImage(img, "PNG", 0, 0, width, height);
      } else if (file.type === "image/jpeg") {
        pdf.addImage(img, "JPEG", 0, 0, width, height);
      }
      const blob = pdf.output("blob");
      const pdfFile = new File([blob], file.name.replace(/\.[^/.]+$/, ".pdf"), { type: "application/pdf" });
      resolve(pdfFile);
    };
    img.onerror = function (err) {
      reject(err);
    };
  });
}

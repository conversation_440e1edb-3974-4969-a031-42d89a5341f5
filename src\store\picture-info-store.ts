import {create} from "zustand";

export interface ICanvasPicture {
  uid: number;
  file: File;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  rotation?: number;
}

interface PictureStore {
  pictures: ICanvasPicture[],
  addPicture: (picture: ICanvasPicture) => void,
  updatePicture: (picture: ICanvasPicture) => void,
  deletePicture: (uid: number) => void,
  inEditMode: boolean,
  setEditMode: (inEditMode: boolean) => void,
}

export const usePictureStore = create<PictureStore>((set) => ({
  pictures: [],
  addPicture: (picture) => set((state) => {
    const pictures = state.pictures
    return {
      pictures: [...pictures, picture],
    }
  }),
  updatePicture: (picture) => set((state) => {
    const pictures = state.pictures
    return {
      pictures: pictures.map((p) => {
        if (p.uid === picture.uid) {
          return picture
        }
        return p
      }),
    }
  }),
  deletePicture: (uid) => set((state) => {
    const pictures = state.pictures
    return {
      pictures: pictures.filter((picture) => picture.uid !== uid),
    }
  }),
  inEditMode: false,
  setEditMode: (inEditMode) => set(() => ({inEditMode})),
}))

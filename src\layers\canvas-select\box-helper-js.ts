import { isEqual } from "lodash";
import { IOcrRectRaw } from "@/db/app-db";
import type { UnifiedBox, UnifiedLine } from "./box-types";

export function unifyingBoxes(
  boxes: IOcrRectRaw[],
  scaleX: number,
  scaleY: number
) {
  const unifiedLines: UnifiedLine[] = [];
  let unifiedBoxes: UnifiedBox[] = [];
  boxes.forEach((box, index) => {
    const boxLine = box.line;
    if (!boxLine) {
      return;
    }

    const boxLineRect = {
      left: boxLine.bbox.x0 * scaleX,
      right: boxLine.bbox.x1 * scaleX,
      top: boxLine.bbox.y0 * scaleY,
      bottom: boxLine.bbox.y1 * scaleY,
    };

    const line = unifiedLines.find(
      (l) => isEqual(l.rect, boxLineRect) && l.text === boxLine.text
    );
    if (line) {
      unifiedBoxes.push({
        uid: box.uid,
        text: box.text,
        confidence: box.confidence,
        lineIndex: line.lineIndex,
        lineHeight: line.rowAttributes.rowHeight * scaleY,
        lineTop: line.rect.top,
        lineBottom: line.rect.bottom,
        rect: {
          left: box.bbox.x0 * scaleX,
          right: box.bbox.x1 * scaleX,
          top: box.bbox.y0 * scaleY,
          bottom: box.bbox.y1 * scaleY,
        },
        line: line,
      });
    } else {
      const unifiedLine: UnifiedLine = {
        lineIndex: unifiedLines.length,
        text: boxLine.text,
        confidence: boxLine.confidence,
        rect: boxLineRect,
        rowAttributes: {
          rowHeight: boxLine.rowAttributes.row_height,
        },
      };
      unifiedLines.push(unifiedLine);
      unifiedBoxes.push({
        uid: box.uid,
        text: box.text,
        confidence: box.confidence,
        lineIndex: unifiedLine.lineIndex,
        lineHeight: unifiedLine.rowAttributes.rowHeight * scaleY,
        lineTop: unifiedLine.rect.top,
        lineBottom: unifiedLine.rect.bottom,
        rect: {
          left: box.bbox.x0 * scaleX,
          right: box.bbox.x1 * scaleX,
          top: box.bbox.y0 * scaleY,
          bottom: box.bbox.y1 * scaleY,
        },
        line: unifiedLine,
      });
    }
  });

  // 高度校正
  unifiedBoxes = unifiedBoxes.map((box) => ({
    ...box,
    rect: {
      left: box.rect.left,
      right: box.rect.right,
      top: Math.max(box.rect.top, box.lineTop),
      bottom: Math.min(box.rect.bottom, box.lineBottom),
    },
  }));

  // 将两个在x轴上有重叠的box分离
  for (let i = 0; i < unifiedBoxes.length; i++) {
    const box1 = unifiedBoxes[i];
    const box2 = unifiedBoxes[i + 1];

    if (!box1 || !box2 || box1.lineIndex !== box2.lineIndex) {
      continue;
    }

    if (box1.rect.right > box2.rect.left) {
      box1.rect.right = box2.rect.left;
    }
  }

  return {
    boxes: unifiedBoxes,
    lines: unifiedLines,
  };
}

export function joinSentences(boxes: UnifiedBox[]) {
  return joinSentencesLocal(boxes);
}

function joinSentencesLocal(boxes: UnifiedBox[]) {
  const sentences = [];
  let prevBox = null;
  let currentSentence = "";
  for (const box of boxes) {
    if (prevBox && box.lineIndex !== prevBox.lineIndex) {
      sentences.push(currentSentence);
      currentSentence = "";
    }
    currentSentence += box.text + " ";
    prevBox = box;
  }

  sentences.push(currentSentence);

  // 将sentences连接成一个字符串
  const s = sentences.length > 1 ? sentences.join("\n") : sentences[0] ?? "";
  return s.trim();
}

export function joinOriginalSentences(
  unifiedBoxes: UnifiedBox[],
  selectedBoxes: UnifiedBox[]
) {
  const selectedBoxesIds = selectedBoxes.map((box) => box.uid);
  const selectedBoxesLineIndexes = selectedBoxes.map((box) => box.lineIndex);

  let originalSentencesPrefixBoxes = [];
  let originalSentencesSuffixBoxes = [];

  let prevBox = null;
  let isPrefixEnd = false;
  let lastReturnLineIndex = -1;
  for (const box of unifiedBoxes) {
    if (selectedBoxesIds.includes(box.uid)) {
      prevBox = box;
      isPrefixEnd = true;

      if (box.line?.text.includes("\n\n")) {
        lastReturnLineIndex = box.lineIndex;
      }

      continue;
    }

    if (!isPrefixEnd) {
      if (
        box.line?.text.includes("\n\n") &&
        !selectedBoxesLineIndexes.includes(box.lineIndex)
      ) {
        originalSentencesPrefixBoxes = [];
      } else if (
        box.text.includes(".") ||
        box.text.includes("?") ||
        box.text.includes("!")
      ) {
        originalSentencesPrefixBoxes = [];
      } else {
        originalSentencesPrefixBoxes.push(box);
      }
    } else {
      if (lastReturnLineIndex !== -1 && box.lineIndex !== lastReturnLineIndex) {
        break;
      }

      originalSentencesSuffixBoxes.push(box);

      if (
        box.text.includes(".") ||
        box.text.includes("?") ||
        box.text.includes("!")
      ) {
        break;
      } else if (box.line?.text.includes("\n\n")) {
        lastReturnLineIndex = box.lineIndex;
      }
    }

    prevBox = box;
  }

  return [
    originalSentencesPrefixBoxes.map((box) => box.text).join(" "),
    originalSentencesSuffixBoxes.map((box) => box.text).join(" "),
  ];
}

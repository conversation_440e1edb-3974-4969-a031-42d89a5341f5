import React, { useEffect, useMemo, useState } from "react";

export type ImageSize = {
  width: number;
  height: number;
};

type ImageContainerCanvasProps = {
  imageFile: File;
  onLoadSuccess?: (imageSize: ImageSize) => void;
};

export default function ImageContainerCanvas(props: ImageContainerCanvasProps) {
  const { imageFile, onLoadSuccess } = props;
  const [imageSrc, setImageSrc] = useState<string>();

  useEffect(() => {
    setImageSrc(URL.createObjectURL(imageFile));

    return () => {
      if (imageSrc) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [imageFile]);

  function onImageLoadSuccess(event: React.SyntheticEvent<HTMLImageElement>) {
    const image = event.currentTarget;
    onLoadSuccess &&
      onLoadSuccess({ width: image.width, height: image.height });
  }

  return useMemo(() => {
    return (
      <img
        src={imageSrc}
        alt={imageFile.name}
        style={{ width: "100%" }}
        onLoad={onImageLoadSuccess}
      />
    );
  }, [imageSrc]);
}

import {create} from "zustand";
import {PDFPageProxy} from "pdfjs-dist";
import {LAYER_TYPE} from "@/constant";

export interface ILayerSize {
  width: number;
  height: number;
}

export interface ILayerScale {
  x: number;
  y: number;
}

export interface IMetadata {
  pdfFileName: string;
  fileType: "PICTURE" | "PDF"
}

type ActiveLayer = LAYER_TYPE | "";

interface PdfInfoStore {
  currentFileId: number;
  setCurrentFileId: (id: number) => void;
  metaData: IMetadata,
  setMetaData: (metaData: IMetadata) => void,
  pdfFile: File[] | File | string | null,
  setPdfFile: (file: File[] | File) => void,
  activeLayer: ActiveLayer,
  setActiveLayer: (layer: ActiveLayer) => void,
  activePageNumber: number,
  setActivePageNumber: (pageNumber: number) => void,
  pdfInfoList: PDFPageProxy[],
  addPdfInfo: (index: number, pdfInfo: PDFPageProxy) => void,
  getMaxPdfHeight: () => number,
  addImgTag: number,
  setAddImgTag: (tag: number) => void,
}

export const usePdfInfoStore = create<PdfInfoStore>((set, get) => ({
  currentFileId: 0,
  setCurrentFileId: (id) => set({currentFileId: id}),
  metaData: {
    pdfFileName: "",
    fileType: "PDF"
  },
  setMetaData: (metaData) => set({metaData: metaData}),
  pdfFile: "",
  setPdfFile: (file) => set({pdfFile: file}),
  activeLayer: LAYER_TYPE.APP,
  setActiveLayer: (layer) => set({activeLayer: layer}),
  activePageNumber: 0,
  setActivePageNumber: (pageNumber) => set({activePageNumber: pageNumber}),
  pdfInfoList: [],
  addPdfInfo: (index, pdfInfo) => set((state) => {
    const pdfInfoList = [...state.pdfInfoList];
    pdfInfoList[index] = pdfInfo;
    return {
      pdfInfoList: pdfInfoList
    }
  }),
  getMaxPdfHeight: () => {
    const pdfInfoList = get().pdfInfoList;
    const maxHeight = Math.max(
      ...pdfInfoList.map((pdfInfo) => pdfInfo?.getViewport({scale: 1}).height ?? 0)
    );

    return isFinite(maxHeight) ? maxHeight : 0;
  },
  addImgTag: 0,
  setAddImgTag: (tag) => set({addImgTag: tag}),
}))

import {useEventListener} from "ahooks";
import {RECT_ACTION_TYPE} from "@/constant";
import {getRectByUID} from "@/db/rect-data-hooks";
import {useRectToolboxStore} from "@/store/rect-toolbox-store";

export default function useRectLayerMessageReceiver() {
  const [activeRect, setActiveRect] = useRectToolboxStore(state => [state.activeRect, state.setActiveRect]);
  const [setScrollRectUid] = useRectToolboxStore(state => [state.setScrollRectUid]);

  useEventListener("message", handleMessageReceive, {});

  function handleMessageReceive(e: MessageEvent) {
    const {type, rect: rectUid} = e.data;
    if (type === RECT_ACTION_TYPE.JUMP && rectUid) {
      getRectByUID(rectUid)
        .then(rect => {
          if (rect) {
            setScrollRectUid(rect.uid)
          }
        })
    } else if (type === RECT_ACTION_TYPE.DELETE && rectUid) {
      if (activeRect?.uid === rectUid) {
        setActiveRect(null);
      }
    }
  }
}
